<?php
session_start();
include('../functions/server.php');
include('../functions/ticket-expiration-functions.php');

// Check if user is logged in and is admin
if (!isset($_SESSION['username'])) {
    header('location: ../front-end/sign-in.php');
    exit();
}

$message = '';
$message_type = '';

// Process sync request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['sync_all'])) {
        // Sync all users
        $query = "SELECT DISTINCT username FROM user WHERE username IS NOT NULL AND username != ''";
        $result = mysqli_query($conn, $query);
        
        $synced_count = 0;
        $total_users = 0;
        
        while ($row = mysqli_fetch_assoc($result)) {
            $total_users++;
            if (syncUserTableTickets($row['username'])) {
                $synced_count++;
            }
        }
        
        $message = "Synced $synced_count out of $total_users users successfully!";
        $message_type = 'success';
        
    } elseif (isset($_POST['sync_single']) && !empty($_POST['username'])) {
        // Sync single user
        $username = mysqli_real_escape_string($conn, $_POST['username']);
        
        if (syncUserTableTickets($username)) {
            $message = "Successfully synced tickets for user: $username";
            $message_type = 'success';
        } else {
            $message = "Failed to sync tickets for user: $username";
            $message_type = 'error';
        }
    }
}

// Get some statistics
$stats_query = "SELECT 
    COUNT(*) as total_users,
    SUM(starter_tickets) as total_starter,
    SUM(premium_tickets) as total_premium,
    SUM(ultimate_tickets) as total_ultimate
FROM user WHERE username IS NOT NULL AND username != ''";
$stats_result = mysqli_query($conn, $stats_query);
$stats = mysqli_fetch_assoc($stats_result);

// Get purchasetickets statistics
$purchase_stats_query = "SELECT 
    ticket_type,
    SUM(remaining_tickets) as total_remaining
FROM purchasetickets 
WHERE remaining_tickets > 0 
AND expiration_date > NOW()
GROUP BY ticket_type";
$purchase_stats_result = mysqli_query($conn, $purchase_stats_query);

$purchase_stats = [];
while ($row = mysqli_fetch_assoc($purchase_stats_result)) {
    $purchase_stats[$row['ticket_type']] = $row['total_remaining'];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sync User Tickets - HelloIT Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-sync-alt"></i> Sync User Tickets</h2>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Admin
                    </a>
                </div>

                <?php if ($message): ?>
                <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- Statistics -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-users"></i> User Table Statistics</h5>
                            </div>
                            <div class="card-body">
                                <p><strong>Total Users:</strong> <?php echo $stats['total_users']; ?></p>
                                <p><strong>Starter Tickets:</strong> <?php echo $stats['total_starter']; ?></p>
                                <p><strong>Premium Tickets:</strong> <?php echo $stats['total_premium']; ?></p>
                                <p><strong>Ultimate Tickets:</strong> <?php echo $stats['total_ultimate']; ?></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-ticket-alt"></i> Purchase Tickets Statistics</h5>
                            </div>
                            <div class="card-body">
                                <p><strong>Starter Remaining:</strong> <?php echo $purchase_stats['starter'] ?? 0; ?></p>
                                <p><strong>Business Remaining:</strong> <?php echo $purchase_stats['business'] ?? 0; ?></p>
                                <p><strong>Premium Remaining:</strong> <?php echo $purchase_stats['premium'] ?? 0; ?></p>
                                <p><strong>Ultimate Remaining:</strong> <?php echo $purchase_stats['ultimate'] ?? 0; ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sync Options -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-sync"></i> Sync All Users</h5>
                            </div>
                            <div class="card-body">
                                <p>This will sync ticket counts for all users based on their actual remaining tickets in the purchasetickets table.</p>
                                <form method="POST">
                                    <button type="submit" name="sync_all" class="btn btn-primary" 
                                            onclick="return confirm('Are you sure you want to sync all users? This will update the user table ticket counts.')">
                                        <i class="fas fa-sync-alt"></i> Sync All Users
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-user-sync"></i> Sync Single User</h5>
                            </div>
                            <div class="card-body">
                                <p>Sync ticket counts for a specific user.</p>
                                <form method="POST">
                                    <div class="mb-3">
                                        <label for="username" class="form-label">Username:</label>
                                        <input type="text" class="form-control" id="username" name="username" 
                                               placeholder="Enter username (e.g., <EMAIL>)" required>
                                    </div>
                                    <button type="submit" name="sync_single" class="btn btn-success">
                                        <i class="fas fa-sync"></i> Sync User
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Information -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle"></i> How It Works</h5>
                    </div>
                    <div class="card-body">
                        <ul>
                            <li><strong>FIFO System:</strong> The system now uses First-In-First-Out logic for ticket usage, tracking individual purchases in the <code>purchasetickets</code> table.</li>
                            <li><strong>User Table:</strong> The <code>user</code> table stores summary counts for quick display in the user interface.</li>
                            <li><strong>Sync Function:</strong> This tool ensures the user table counts match the actual remaining tickets from the purchasetickets table.</li>
                            <li><strong>Automatic Sync:</strong> The system now automatically syncs user table counts whenever tickets are used via the FIFO system.</li>
                            <li><strong>Manual Sync:</strong> Use this tool to fix any discrepancies that may have occurred before the automatic sync was implemented.</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
