<?php
session_start();
include('../functions/server.php');
require_once '../vendor/autoload.php'; // Include Composer autoloader for Guzzle

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login');
    exit();
}

$admin_username = $_SESSION['admin_username'];
$admin_id = $_SESSION['admin_id'];
$admin_role = $_SESSION['admin_role'];

// Include customer sync functions
require_once '../functions/appika_customer_sync.php';

// Load centralized API configuration
require_once '../config/api-config.php';

// Get API configuration
$apiConfig = getCustomerApiConfig();
$apiEndpoint = $apiConfig['endpoint'];
$apiPath = $apiConfig['path'];
$apiKey = $apiConfig['key'];

// Function to send user data to Appika API
function sendToAppikaAPI($customerData, $method = 'POST', $path = '') {
    global $apiEndpoint, $apiPath, $apiKey;

    // Create a Guzzle HTTP client
    $client = new \GuzzleHttp\Client([
        'base_uri' => $apiEndpoint,
        'timeout' => 30,
        'http_errors' => false, // Don't throw exceptions for 4xx/5xx responses
    ]);

    // Determine the full path
    $fullPath = empty($path) ? $apiPath : $path;

    try {
        // Send the request
        $response = $client->request($method, $fullPath, [
            'headers' => [
                'Authorization' => "Bearer {$apiKey}",
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ],
            'json' => $customerData
        ]);

        // Get status code and response body
        $statusCode = $response->getStatusCode();
        $body = $response->getBody()->getContents();

        // Parse JSON if the response is JSON
        if (strpos($response->getHeaderLine('Content-Type'), 'application/json') !== false) {
            $data = json_decode($body, true);
            return [
                'success' => ($statusCode >= 200 && $statusCode < 300),
                'status' => $statusCode,
                'data' => $data
            ];
        } else {
            return [
                'success' => false,
                'status' => $statusCode,
                'data' => $body
            ];
        }
    } catch (\Exception $e) {
        return [
            'success' => false,
            'status' => 500,
            'error' => $e->getMessage()
        ];
    }
}

// Function to add location to a customer in Appika
function addLocationToAppika($customerDbId, $locationData) {
    global $apiEndpoint, $apiPath, $apiKey;

    // Create the path for adding a location
    $locationPath = $apiPath . '/' . $customerDbId . '/locations';

    // Send the location data to Appika
    return sendToAppikaAPI($locationData, 'POST', $locationPath);
}

// Function to update location in Appika
function updateLocationInAppika($customerDbId, $locationId, $locationData) {
    global $apiEndpoint, $apiPath, $apiKey;

    // Create the path for updating a location
    $locationPath = $apiPath . '/' . $customerDbId . '/locations/' . $locationId;

    // Send the location data to Appika
    return sendToAppikaAPI($locationData, 'PUT', $locationPath);
}

$message = '';
$message_type = '';
$simulation_result = null;

// Check if customer sync columns exist
$columns_exist = false;
$check_columns = mysqli_query($conn, "SHOW COLUMNS FROM user LIKE 'appika_updated_at'");
if (mysqli_num_rows($check_columns) > 0) {
    $columns_exist = true;
}

// Get users with Appika IDs for simulation (either appika_id or appika_customer_id)
$users_with_appika = [];
$users_query = "SELECT id, username, email, first_name, company_name, appika_id, appika_customer_id
                FROM user
                WHERE (appika_id IS NOT NULL AND appika_id != '')
                   OR (appika_customer_id IS NOT NULL AND appika_customer_id != '')
                ORDER BY username ASC
                LIMIT 50";
$users_result = mysqli_query($conn, $users_query);
while ($user = mysqli_fetch_assoc($users_result)) {
    $users_with_appika[] = $user;
}

// Process simulation
if (isset($_POST['simulate_update'])) {
    $user_id = intval($_POST['user_id']);
    $simulate_changes = isset($_POST['simulate_changes']) ? $_POST['simulate_changes'] : [];
    
    if ($user_id > 0 && !empty($simulate_changes)) {
        // Get user data
        $user_query = "SELECT * FROM user WHERE id = $user_id";
        $user_result = mysqli_query($conn, $user_query);
        $user = mysqli_fetch_assoc($user_result);
        
        if ($user) {
            // Simulate the update by temporarily modifying the database
            $update_fields = [];
            $simulated_changes = [];
            
            foreach ($simulate_changes as $field) {
                switch ($field) {
                    case 'name':
                        $new_first_name = $user['first_name'] . ' (Updated)';
                        $new_last_name = $user['last_name'] . ' (Sim)';
                        $update_fields[] = "first_name = '" . mysqli_real_escape_string($conn, $new_first_name) . "'";
                        $update_fields[] = "last_name = '" . mysqli_real_escape_string($conn, $new_last_name) . "'";
                        $simulated_changes['first_name'] = ['old' => $user['first_name'], 'new' => $new_first_name];
                        $simulated_changes['last_name'] = ['old' => $user['last_name'], 'new' => $new_last_name];
                        break;

                    case 'phone':
                        $new_phone = '999-' . substr($user['tell'], -7);
                        $update_fields[] = "tell = '" . mysqli_real_escape_string($conn, $new_phone) . "'";
                        $simulated_changes['tell'] = ['old' => $user['tell'], 'new' => $new_phone];
                        break;
                    case 'company':
                        $new_company = $user['company_name'] . ' (Simulated)';
                        $update_fields[] = "company_name = '" . mysqli_real_escape_string($conn, $new_company) . "'";
                        $simulated_changes['company_name'] = ['old' => $user['company_name'], 'new' => $new_company];
                        break;

                    case 'tax_id':
                        $new_tax_id = ($user['tax_id'] ?: 'TAX') . '_SIM';
                        $update_fields[] = "tax_id = '" . mysqli_real_escape_string($conn, $new_tax_id) . "'";
                        $simulated_changes['tax_id'] = ['old' => $user['tax_id'], 'new' => $new_tax_id];
                        break;
                    case 'address':
                        $new_address = ($user['address'] ?: 'Address') . ' (Updated)';
                        $update_fields[] = "address = '" . mysqli_real_escape_string($conn, $new_address) . "'";
                        $simulated_changes['address'] = ['old' => $user['address'], 'new' => $new_address];
                        break;
                    case 'address2':
                        $new_address2 = ($user['address2'] ?: 'Address Line 2') . ' (Simulated)';
                        $update_fields[] = "address2 = '" . mysqli_real_escape_string($conn, $new_address2) . "'";
                        $simulated_changes['address2'] = ['old' => $user['address2'], 'new' => $new_address2];
                        break;
                    case 'district':
                        $new_district = ($user['district'] ?: 'District') . ' Sim';
                        $update_fields[] = "district = '" . mysqli_real_escape_string($conn, $new_district) . "'";
                        $simulated_changes['district'] = ['old' => $user['district'], 'new' => $new_district];
                        break;
                    case 'city':
                        $new_city = ($user['city'] ?: 'City') . ' (Updated)';
                        $update_fields[] = "city = '" . mysqli_real_escape_string($conn, $new_city) . "'";
                        $simulated_changes['city'] = ['old' => $user['city'], 'new' => $new_city];
                        break;
                    case 'state':
                        $new_state = ($user['state'] ?: 'State') . ' (Sim)';
                        $update_fields[] = "state = '" . mysqli_real_escape_string($conn, $new_state) . "'";
                        $simulated_changes['state'] = ['old' => $user['state'], 'new' => $new_state];
                        break;
                    case 'postal_code':
                        $new_postal_code = ($user['postal_code'] ?: '12345') . '9';
                        $update_fields[] = "postal_code = '" . mysqli_real_escape_string($conn, $new_postal_code) . "'";
                        $simulated_changes['postal_code'] = ['old' => $user['postal_code'], 'new' => $new_postal_code];
                        break;

                }
            }
            
            if (!empty($update_fields)) {
                $simulation_steps = [];
                $appika_success = false;

                // Step 1: Update customer in Appika (if they have an Appika ID)
                if (!empty($user['appika_id'])) {
                    $simulation_steps[] = "Step 1: Updating customer in Appika...";

                    // Search for the customer in Appika to get their database ID
                    $client = new \GuzzleHttp\Client([
                        'base_uri' => $apiEndpoint,
                        'timeout' => 30,
                        'http_errors' => false,
                    ]);

                    // Search for customer by appika_id
                    $searchResult = $client->request('GET', $apiPath, [
                        'headers' => [
                            'Authorization' => "Bearer {$apiKey}",
                            'Accept' => 'application/json',
                        ],
                        'query' => [
                            'no' => $user['appika_id']
                        ]
                    ]);

                    $searchData = json_decode($searchResult->getBody()->getContents(), true);

                    if (isset($searchData['items']) && !empty($searchData['items'])) {
                        $customerData = $searchData['items'][0];
                        $customerDbId = $customerData['id'];

                        // Get the new values from simulated changes
                        $newFirstName = isset($simulated_changes['first_name']) ? $simulated_changes['first_name']['new'] : $user['first_name'];
                        $newLastName = isset($simulated_changes['last_name']) ? $simulated_changes['last_name']['new'] : $user['last_name'];

                        // Prepare customer data for update
                        $customerName = $newFirstName . ' ' . $newLastName;
                        $customerStartDate = date('Y-m-d', strtotime($customerData['start_date']));

                        // Set fixed values as per requirements
                        $customerGroup = '10';
                        $ofcId = '511';
                        $assignTo = '1';
                        $creator = '1';
                        $status = 'a';

                        // Create customer data structure for Appika API update
                        $updateData = [
                            'no' => $user['appika_id'],
                            'name' => $customerName,
                            'entity_type' => '1', // 1 for COMPANY, 2 for INDIVIDUAL
                            'grp_id' => $customerGroup,
                            'ofc_id' => $ofcId,
                            'assign2' => $assignTo,
                            'creator' => $creator,
                            'start_date' => $customerStartDate,
                            'status' => $status
                        ];

                        // Update customer in Appika
                        $updatePath = $apiPath . '/' . $customerDbId;
                        $updateResult = sendToAppikaAPI($updateData, 'PUT', $updatePath);

                        if ($updateResult['success']) {
                            $simulation_steps[] = "✅ Step 1a Complete: Customer updated in Appika successfully";

                            // Step 1b: Update customer location if address fields are being simulated
                            $addressFieldsChanged = array_intersect(['address', 'address2', 'district', 'city', 'state', 'postal_code'], $simulate_changes);
                            if (!empty($addressFieldsChanged)) {
                                $simulation_steps[] = "Step 1b: Updating customer location in Appika...";

                                // Get customer locations to find primary location
                                try {
                                    $locationsResult = $client->request('GET', $apiPath . '/' . $customerDbId . '/locations', [
                                        'headers' => [
                                            'Authorization' => "Bearer {$apiKey}",
                                            'Accept' => 'application/json',
                                        ]
                                    ]);

                                    $locationsData = json_decode($locationsResult->getBody()->getContents(), true);
                                    $primaryLocationId = null;

                                    // Find primary location
                                    if (isset($locationsData['items']) && !empty($locationsData['items'])) {
                                        foreach ($locationsData['items'] as $location) {
                                            if (isset($location['is_primary_loc']) && $location['is_primary_loc'] === 'y') {
                                                $primaryLocationId = $location['id'];
                                                break;
                                            }
                                        }
                                    }

                                    // Prepare location data with simulated changes
                                    $newAddress = isset($simulated_changes['address']) ? $simulated_changes['address']['new'] : $user['address'];
                                    $newAddress2 = isset($simulated_changes['address2']) ? $simulated_changes['address2']['new'] : $user['address2'];
                                    $newCity = isset($simulated_changes['city']) ? $simulated_changes['city']['new'] : $user['city'];
                                    $newState = isset($simulated_changes['state']) ? $simulated_changes['state']['new'] : $user['state'];
                                    $newPostalCode = isset($simulated_changes['postal_code']) ? $simulated_changes['postal_code']['new'] : $user['postal_code'];

                                    $locationData = [
                                        'loc_code' => 'LOC-' . $user['appika_id'],
                                        'loc_name' => $customerName . ' Location',
                                        'add1' => $newAddress ?: 'Address',
                                        'add2' => $newAddress2 ?: '',
                                        'ccode' => $user['country'] ?: 'TH',
                                        'state_code' => $newState ?: '10',
                                        'city' => $newCity ?: 'Bangkok',
                                        'status' => 'a',
                                        'is_primary_loc' => 'y',
                                        'zip' => $newPostalCode ?: '10230',
                                        'parent_id' => 0
                                    ];

                                    // Update or create location
                                    if ($primaryLocationId) {
                                        $locationResult = updateLocationInAppika($customerDbId, $primaryLocationId, $locationData);
                                        if ($locationResult['success']) {
                                            $simulation_steps[] = "✅ Step 1b Complete: Customer location updated in Appika successfully";
                                        } else {
                                            $simulation_steps[] = "⚠️ Step 1b Warning: Customer location update failed - " . ($locationResult['error'] ?? 'Unknown error');
                                        }
                                    } else {
                                        $locationResult = addLocationToAppika($customerDbId, $locationData);
                                        if ($locationResult['success']) {
                                            $simulation_steps[] = "✅ Step 1b Complete: Customer location created in Appika successfully";
                                        } else {
                                            $simulation_steps[] = "⚠️ Step 1b Warning: Customer location creation failed - " . ($locationResult['error'] ?? 'Unknown error');
                                        }
                                    }
                                } catch (\Exception $e) {
                                    $simulation_steps[] = "⚠️ Step 1b Warning: Could not update location - " . $e->getMessage();
                                }
                            }

                            $appika_success = true;
                        } else {
                            $simulation_steps[] = "❌ Step 1 Failed: Could not update customer in Appika - " . ($updateResult['error'] ?? 'Unknown error');
                        }
                    } else {
                        $simulation_steps[] = "❌ Step 1 Failed: Customer not found in Appika";
                    }
                } else {
                    $simulation_steps[] = "⚠️ Step 1 Skipped: User has no Appika ID";
                }

                // Step 2: Update the local database
                $simulation_steps[] = "Step 2: Updating local database...";
                $update_fields[] = "appika_updated_at = NOW()";
                $update_fields[] = "appika_update_source = 'simulation'";

                $update_sql = "UPDATE user SET " . implode(', ', $update_fields) . " WHERE id = $user_id";

                if (mysqli_query($conn, $update_sql)) {
                    $simulation_steps[] = "✅ Step 2 Complete: Local database updated successfully";

                    // Log the simulation
                    if (function_exists('logCustomerSyncActivity')) {
                        logCustomerSyncActivity($user_id, 'simulation', true, 'Simulated Appika customer update', $simulated_changes);
                    }

                    $simulation_result = [
                        'success' => true,
                        'appika_success' => $appika_success,
                        'user' => $user,
                        'changes' => $simulated_changes,
                        'steps' => $simulation_steps
                    ];

                    if ($appika_success) {
                        $message = "Successfully simulated complete Appika customer update (both Appika and local database) for user: " . htmlspecialchars($user['username']);
                    } else {
                        $message = "Simulated local database update for user: " . htmlspecialchars($user['username']) . " (Appika update failed - see details below)";
                    }
                    $message_type = 'success';
                } else {
                    $simulation_steps[] = "❌ Step 2 Failed: " . mysqli_error($conn);
                    $simulation_result = [
                        'success' => false,
                        'appika_success' => $appika_success,
                        'steps' => $simulation_steps
                    ];
                    $message = "Error simulating update: " . mysqli_error($conn);
                    $message_type = 'danger';
                }
            }
        } else {
            $message = "User not found";
            $message_type = 'danger';
        }
    } else {
        $message = "Please select a user and at least one field to simulate";
        $message_type = 'warning';
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simulate Appika Customer Update</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap, fonts & icons -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Vendor stylesheets -->
    <link rel="stylesheet" href="../css/main.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .admin-container {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        .admin-header {
            background-color: #fff;
            padding: 15px 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }
        .admin-content {
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        .simulation-form {
            max-width: 600px;
            margin: 0 auto;
        }
        .setup-notice {
            text-align: center;
            padding: 40px;
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            color: #856404;
        }
        .result-box {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        .change-item {
            background-color: #fff;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 10px;
            margin-bottom: 10px;
        }
        .old-value {
            color: #dc3545;
            text-decoration: line-through;
        }
        .new-value {
            color: #28a745;
            font-weight: bold;
        }
        .simulation-steps {
            margin: 15px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 6px;
            border-left: 3px solid #007bff;
        }
        .steps-log {
            background-color: #fff;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 0.9em;
        }
        .step-item {
            padding: 3px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .step-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1><i class="fas fa-flask text-info"></i> Simulate Appika Customer Update</h1>
            <div class="admin-user">
                <span>Welcome, <?php echo htmlspecialchars($admin_username); ?></span>
                <a href="admin-logout.php" class="btn btn-outline-danger btn-sm">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </div>
        </div>

        <div class="admin-content">
            <div class="mb-4">
                <a href="admin-users.php" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Users
                </a>
                <a href="appika-customer-updates-log.php" class="btn btn-outline-primary ml-2">
                    <i class="fas fa-list"></i> View Updates Log
                </a>
            </div>

            <?php if (!empty($message)): ?>
            <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                <?php echo $message; ?>
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <?php endif; ?>

            <?php if (!$columns_exist): ?>
            <div class="setup-notice">
                <i class="fas fa-exclamation-triangle text-warning mb-3" style="font-size: 2em;"></i>
                <h4>Setup Required</h4>
                <p>Appika customer sync is not set up yet. Run the setup script to enable simulation functionality.</p>
                <a href="../setup/setup-customer-sync.php" class="btn btn-warning">
                    <i class="fas fa-cog"></i> Run Setup
                </a>
            </div>
            <?php elseif (empty($users_with_appika)): ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                <strong>No Users Available</strong><br>
                No users with Appika IDs found. Users need to be uploaded to Appika before simulation can be performed.
                <br><br>
                <a href="admin-users.php" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Go to Users Page to Add Users to Appika
                </a>
            </div>
            <?php else: ?>
            <div class="simulation-form">
                <h4 class="mb-4">Simulate Customer Update from Appika</h4>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Important:</strong> This tool will update both your local database AND the actual customer data in Appika.
                    Use with caution and only on test customers.
                </div>
                <p class="text-muted mb-4">
                    This tool simulates a complete customer update cycle: it updates the customer in Appika first,
                    then updates your local database to match, just like a real sync operation.
                </p>

                <form method="POST">
                    <div class="form-group">
                        <label for="user_id"><strong>Select Customer:</strong></label>
                        <select name="user_id" id="user_id" class="form-control" required>
                            <option value="">Choose a customer...</option>
                            <?php foreach ($users_with_appika as $user): ?>
                            <option value="<?php echo $user['id']; ?>">
                                <?php
                                $appika_display_id = $user['appika_id'] ?: $user['appika_customer_id'];
                                echo htmlspecialchars($appika_display_id);
                                ?>
                                (<?php echo htmlspecialchars($user['email']); ?>) -
                                <?php echo htmlspecialchars($appika_display_id); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label><strong>Fields to Simulate Changes:</strong></label>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="simulate_changes[]" value="name" id="change_name">
                                    <label class="form-check-label" for="change_name">
                                        Customer Name (First & Last Name)
                                    </label>
                                </div>

                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="simulate_changes[]" value="phone" id="change_phone">
                                    <label class="form-check-label" for="change_phone">
                                        Phone Number
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="simulate_changes[]" value="company" id="change_company">
                                    <label class="form-check-label" for="change_company">
                                        Company Name
                                    </label>
                                </div>

                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="simulate_changes[]" value="tax_id" id="change_tax_id">
                                    <label class="form-check-label" for="change_tax_id">
                                        Tax ID
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="simulate_changes[]" value="address" id="change_address">
                                    <label class="form-check-label" for="change_address">
                                        Address
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="simulate_changes[]" value="address2" id="change_address2">
                                    <label class="form-check-label" for="change_address2">
                                        Address Line 2
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="simulate_changes[]" value="district" id="change_district">
                                    <label class="form-check-label" for="change_district">
                                        District
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="simulate_changes[]" value="city" id="change_city">
                                    <label class="form-check-label" for="change_city">
                                        City
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="simulate_changes[]" value="state" id="change_state">
                                    <label class="form-check-label" for="change_state">
                                        State/Province
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="simulate_changes[]" value="postal_code" id="change_postal_code">
                                    <label class="form-check-label" for="change_postal_code">
                                        Postal Code
                                    </label>
                                </div>

                            </div>
                        </div>
                    </div>

                    <button type="submit" name="simulate_update" class="btn btn-info btn-lg btn-block">
                        <i class="fas fa-flask"></i> Simulate Appika Update
                    </button>
                </form>

                <?php if ($simulation_result): ?>
                <div class="result-box">
                    <h5>
                        <i class="fas fa-<?php echo $simulation_result['success'] ? 'check-circle text-success' : 'exclamation-triangle text-warning'; ?>"></i>
                        Simulation <?php echo $simulation_result['success'] ? 'Successful' : 'Completed with Issues'; ?>
                    </h5>

                    <?php if (isset($simulation_result['steps']) && !empty($simulation_result['steps'])): ?>
                    <div class="simulation-steps">
                        <h6><i class="fas fa-list"></i> Execution Steps:</h6>
                        <div class="steps-log">
                            <?php foreach ($simulation_result['steps'] as $step): ?>
                            <div class="step-item"><?php echo htmlspecialchars($step); ?></div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if ($simulation_result['success']): ?>
                    <p><strong>Customer:</strong> <?php echo htmlspecialchars($simulation_result['user']['username']); ?></p>

                    <?php if (isset($simulation_result['appika_success'])): ?>
                    <div class="alert alert-<?php echo $simulation_result['appika_success'] ? 'success' : 'warning'; ?> mt-3">
                        <strong>Appika Update:</strong>
                        <?php if ($simulation_result['appika_success']): ?>
                            ✅ Customer successfully updated in Appika
                        <?php else: ?>
                            ❌ Failed to update customer in Appika (local database was updated)
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>

                    <p><strong>Changes Applied:</strong></p>

                    <?php foreach ($simulation_result['changes'] as $field => $change): ?>
                    <div class="change-item">
                        <strong><?php echo ucfirst(str_replace('_', ' ', $field)); ?>:</strong><br>
                        <span class="old-value"><?php echo htmlspecialchars($change['old']); ?></span><br>
                        <span class="new-value"><?php echo htmlspecialchars($change['new']); ?></span>
                    </div>
                    <?php endforeach; ?>

                    <div class="mt-3">
                        <a href="admin-users.php" class="btn btn-primary">
                            <i class="fas fa-users"></i> View Updated Customer in Users List
                        </a>
                        <a href="appika-customer-updates-log.php" class="btn btn-outline-primary ml-2">
                            <i class="fas fa-list"></i> View in Updates Log
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="../js/bootstrap.bundle.min.js"></script>
</body>
</html>
