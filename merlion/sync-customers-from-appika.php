<?php
/**
 * Scheduled Customer Sync Script - Run via cron job
 * Syncs customer updates FROM Appika TO local database
 * 
 * Usage: php sync-customers-from-appika.php
 * Cron: Run every 10 minutes with /usr/bin/php /path/to/sync-customers-from-appika.php
 */

// Include required files
include('../functions/server.php');
require_once '../functions/appika_customer_sync.php';

// Set execution time limit for long-running sync
set_time_limit(300); // 5 minutes max

// Log file for sync operations
$logFile = '../logs/appika_customer_sync_' . date('Y-m-d') . '.log';

// Ensure logs directory exists
if (!file_exists('../logs')) {
    mkdir('../logs', 0755, true);
}

// Function to write to log
function writeLog($message) {
    global $logFile;
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[$timestamp] $message\n", FILE_APPEND | LOCK_EX);
}

// Start sync process
writeLog("=== Starting Appika Customer Reverse Sync ===");

try {
    // Perform bulk customer sync
    $results = bulkSyncCustomersFromAppika(100); // Check up to 100 customers
    
    // Log results
    writeLog("Customer sync completed:");
    writeLog("- Total customers checked: " . $results['total_checked']);
    writeLog("- Total customers updated: " . $results['total_updated']);
    
    if (!empty($results['errors'])) {
        writeLog("- Errors encountered:");
        foreach ($results['errors'] as $error) {
            writeLog("  * $error");
        }
    }
    
    // Output for cron job logs
    echo "Appika Customer Sync: {$results['total_checked']} checked, {$results['total_updated']} updated";
    if (!empty($results['errors'])) {
        echo ", " . count($results['errors']) . " errors";
    }
    echo "\n";
    
} catch (Exception $e) {
    $errorMsg = "Fatal error during customer sync: " . $e->getMessage();
    writeLog($errorMsg);
    echo $errorMsg . "\n";
    exit(1);
}

writeLog("=== Customer sync completed successfully ===\n");
?>
