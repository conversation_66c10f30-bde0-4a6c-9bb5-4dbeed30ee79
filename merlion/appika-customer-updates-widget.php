<?php
/**
 * <PERSON><PERSON>ika Customer Updates Widget
 * Shows recent customer sync activity and allows manual sync
 */

// Include required files
include('../functions/server.php');
require_once '../functions/appika_customer_sync.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login');
    exit();
}

// Handle manual sync request
if (isset($_POST['manual_sync'])) {
    $sync_results = bulkSyncCustomersFromAppika(50);
    $manual_sync_message = "Manual sync completed: {$sync_results['total_checked']} checked, {$sync_results['total_updated']} updated";
    if (!empty($sync_results['errors'])) {
        $manual_sync_message .= ", " . count($sync_results['errors']) . " errors";
    }
}

// Get recent sync logs
$recent_logs_query = "SELECT csl.*, u.username, u.email 
                      FROM customer_sync_logs csl
                      LEFT JOIN user u ON csl.user_id = u.id
                      ORDER BY csl.created_at DESC 
                      LIMIT 20";
$recent_logs_result = mysqli_query($conn, $recent_logs_query);

// Get sync statistics for today
$today_stats_query = "SELECT 
                        COUNT(*) as total_syncs,
                        SUM(success) as successful_syncs,
                        COUNT(*) - SUM(success) as failed_syncs
                      FROM customer_sync_logs 
                      WHERE DATE(created_at) = CURDATE()";
$today_stats_result = mysqli_query($conn, $today_stats_query);
$today_stats = mysqli_fetch_assoc($today_stats_result);

// Get last sync time
$last_sync_query = "SELECT MAX(created_at) as last_sync FROM customer_sync_logs";
$last_sync_result = mysqli_query($conn, $last_sync_query);
$last_sync = mysqli_fetch_assoc($last_sync_result);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Appika Customer Updates</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sync-status {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        .sync-success { background-color: #d4edda; color: #155724; }
        .sync-error { background-color: #f8d7da; color: #721c24; }
        .sync-info { background-color: #d1ecf1; color: #0c5460; }
        .log-entry {
            border-left: 4px solid #007bff;
            padding: 10px;
            margin-bottom: 10px;
            background-color: #f8f9fa;
        }
        .log-success { border-left-color: #28a745; }
        .log-error { border-left-color: #dc3545; }
        .changes-list {
            font-size: 0.9em;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-sync-alt"></i> Appika Customer Updates</h2>
                    <div>
                        <a href="admin-users.php" class="btn btn-secondary">
                            <i class="fas fa-users"></i> Back to Users
                        </a>
                        <form method="POST" style="display: inline;">
                            <button type="submit" name="manual_sync" class="btn btn-primary">
                                <i class="fas fa-sync"></i> Manual Sync
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Manual sync result -->
                <?php if (isset($manual_sync_message)): ?>
                <div class="sync-status sync-info">
                    <i class="fas fa-info-circle"></i> <?php echo htmlspecialchars($manual_sync_message); ?>
                </div>
                <?php endif; ?>

                <!-- Sync Statistics -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5 class="card-title">Today's Syncs</h5>
                                <h3 class="text-primary"><?php echo $today_stats['total_syncs'] ?? 0; ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5 class="card-title">Successful</h5>
                                <h3 class="text-success"><?php echo $today_stats['successful_syncs'] ?? 0; ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5 class="card-title">Failed</h5>
                                <h3 class="text-danger"><?php echo $today_stats['failed_syncs'] ?? 0; ?></h3>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Last Sync Info -->
                <?php if ($last_sync['last_sync']): ?>
                <div class="sync-status sync-info">
                    <i class="fas fa-clock"></i> Last sync: <?php echo date('Y-m-d H:i:s', strtotime($last_sync['last_sync'])); ?>
                    (<?php echo time_elapsed_string($last_sync['last_sync']); ?> ago)
                </div>
                <?php endif; ?>

                <!-- Recent Sync Logs -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-list"></i> Recent Sync Activity</h5>
                    </div>
                    <div class="card-body">
                        <?php if (mysqli_num_rows($recent_logs_result) > 0): ?>
                            <?php while ($log = mysqli_fetch_assoc($recent_logs_result)): ?>
                            <div class="log-entry <?php echo $log['success'] ? 'log-success' : 'log-error'; ?>">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <strong>
                                            <?php if ($log['username']): ?>
                                                <?php echo htmlspecialchars($log['username']); ?>
                                                (<?php echo htmlspecialchars($log['email']); ?>)
                                            <?php else: ?>
                                                User ID: <?php echo $log['user_id']; ?>
                                            <?php endif; ?>
                                        </strong>
                                        <span class="badge bg-<?php echo $log['success'] ? 'success' : 'danger'; ?>">
                                            <?php echo $log['success'] ? 'Success' : 'Failed'; ?>
                                        </span>
                                    </div>
                                    <small class="text-muted">
                                        <?php echo date('M j, Y H:i', strtotime($log['created_at'])); ?>
                                    </small>
                                </div>
                                
                                <div class="mt-2">
                                    <strong>Operation:</strong> <?php echo htmlspecialchars($log['operation']); ?><br>
                                    <strong>Message:</strong> <?php echo htmlspecialchars($log['message']); ?>
                                </div>

                                <?php if (!empty($log['changes'])): ?>
                                    <?php $changes = json_decode($log['changes'], true); ?>
                                    <div class="changes-list mt-2">
                                        <strong>Changes made:</strong>
                                        <ul class="mb-0">
                                            <?php foreach ($changes as $field => $change): ?>
                                            <li>
                                                <strong><?php echo ucfirst(str_replace('_', ' ', $field)); ?>:</strong>
                                                "<?php echo htmlspecialchars($change['old']); ?>" → 
                                                "<?php echo htmlspecialchars($change['new']); ?>"
                                            </li>
                                            <?php endforeach; ?>
                                        </ul>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-inbox fa-3x mb-3"></i>
                                <p>No sync activity found</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php
// Helper function to calculate time elapsed
function time_elapsed_string($datetime, $full = false) {
    $now = new DateTime;
    $ago = new DateTime($datetime);
    $diff = $now->diff($ago);

    $diff->w = floor($diff->d / 7);
    $diff->d -= $diff->w * 7;

    $string = array(
        'y' => 'year',
        'm' => 'month',
        'w' => 'week',
        'd' => 'day',
        'h' => 'hour',
        'i' => 'minute',
        's' => 'second',
    );
    foreach ($string as $k => &$v) {
        if ($diff->$k) {
            $v = $diff->$k . ' ' . $v . ($diff->$k > 1 ? 's' : '');
        } else {
            unset($string[$k]);
        }
    }

    if (!$full) $string = array_slice($string, 0, 1);
    return $string ? implode(', ', $string) : 'just now';
}
?>
