<?php
session_start();
include('../functions/server.php');

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login');
    exit();
}

$admin_username = $_SESSION['admin_username'];
$admin_id = $_SESSION['admin_id'];
$admin_role = $_SESSION['admin_role'];

// Include customer sync functions
require_once '../functions/appika_customer_sync.php';

// Pagination settings
$items_per_page = 20;
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$offset = ($page - 1) * $items_per_page;

// Time filter
$timeFilter = isset($_GET['time']) ? $_GET['time'] : '24h';

// Search functionality
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$where_clause = '';
$search_params = [];

if (!empty($search)) {
    $where_clause = "WHERE (u.username LIKE ? OR u.email LIKE ? OR u.first_name LIKE ? OR u.company_name LIKE ?)";
    $search_term = "%$search%";
    $search_params = [$search_term, $search_term, $search_term, $search_term];
}

// Check if customer sync columns exist
$columns_exist = false;
$check_columns = mysqli_query($conn, "SHOW COLUMNS FROM user LIKE 'appika_updated_at'");
if (mysqli_num_rows($check_columns) > 0) {
    $columns_exist = true;
}

$updates = [];
$total_count = 0;

if ($columns_exist) {
    // Build the WHERE clause with time filter
    $appika_condition = "u.appika_updated_at IS NOT NULL";

    // Add time filter condition
    $time_condition = '';
    switch ($timeFilter) {
        case '1h':
            $time_condition = "AND u.appika_updated_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)";
            break;
        case '24h':
            $time_condition = "AND u.appika_updated_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)";
            break;
        case '7d':
            $time_condition = "AND u.appika_updated_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
            break;
        case '30d':
            $time_condition = "AND u.appika_updated_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
            break;
        case 'all':
        default:
            $time_condition = '';
            break;
    }

    if (!empty($where_clause)) {
        $final_where_clause = "$where_clause AND $appika_condition $time_condition";
    } else {
        $final_where_clause = "WHERE $appika_condition $time_condition";
    }

    // Get total count for pagination
    $count_sql = "SELECT COUNT(*) as total FROM user u $final_where_clause";
    if (!empty($search_params)) {
        $count_stmt = mysqli_prepare($conn, $count_sql);
        mysqli_stmt_bind_param($count_stmt, str_repeat('s', count($search_params)), ...$search_params);
        mysqli_stmt_execute($count_stmt);
        $count_result = mysqli_stmt_get_result($count_stmt);
    } else {
        $count_result = mysqli_query($conn, $count_sql);
    }
    $total_count = mysqli_fetch_assoc($count_result)['total'];

    // Get customer updates with pagination and sync log details
    $updates_sql = "SELECT u.id, u.username, u.email, u.first_name, u.company_name,
                           u.appika_id, u.appika_customer_id, u.appika_updated_at, u.appika_update_source,
                           csl.changes, csl.message as sync_message, csl.operation as sync_operation
                    FROM user u
                    LEFT JOIN customer_sync_logs csl ON u.id = csl.user_id
                        AND csl.created_at = (
                            SELECT MAX(created_at)
                            FROM customer_sync_logs
                            WHERE user_id = u.id AND success = 1
                        )
                    $final_where_clause
                    ORDER BY u.appika_updated_at DESC
                    LIMIT ? OFFSET ?";
    
    $stmt = mysqli_prepare($conn, $updates_sql);
    if (!empty($search_params)) {
        $params = array_merge($search_params, [$items_per_page, $offset]);
        $types = str_repeat('s', count($search_params)) . 'ii';
        mysqli_stmt_bind_param($stmt, $types, ...$params);
    } else {
        mysqli_stmt_bind_param($stmt, 'ii', $items_per_page, $offset);
    }
    
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    while ($row = mysqli_fetch_assoc($result)) {
        $updates[] = $row;
    }
}

// Calculate pagination
$total_pages = ceil($total_count / $items_per_page);

// Function to format time ago
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . ' min ago';
    if ($time < 86400) return floor($time/3600) . ' hr ago';
    if ($time < 2592000) return floor($time/86400) . ' days ago';
    
    return date('M j, Y g:i A', strtotime($datetime));
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Appika Customer Updates Log</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap, fonts & icons -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Vendor stylesheets -->
    <link rel="stylesheet" href="../css/main.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .admin-container {
            padding: 20px;
            max-width: 100%;
            overflow-x: hidden;
        }
        .admin-header {
            background-color: #fff;
            padding: 15px 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }
        .admin-content {
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            padding: 20px;
            min-height: calc(100vh - 120px);
        }
        .update-item {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: #fff;
        }
        .update-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .customer-info {
            flex: 1;
        }
        .update-time {
            color: #6c757d;
            font-size: 0.9em;
        }
        .badge-info {
            background-color: #17a2b8;
            color: #fff;
            font-family: 'Courier New', monospace;
            font-weight: bold;
            font-size: 11px;
            padding: 3px 6px;
        }
        .no-updates {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }
        .setup-notice {
            text-align: center;
            padding: 40px;
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            color: #856404;
        }
        .filter-tabs {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            padding: 15px 20px;
        }
        .filter-tabs .nav-link {
            color: #666;
            border: none;
            padding: 8px 16px;
            margin-right: 5px;
            border-radius: 20px;
            transition: all 0.3s;
        }
        .filter-tabs .nav-link.active {
            background-color: #473BF0;
            color: white;
        }
        .filter-tabs .nav-link:hover {
            background-color: #f8f9fa;
            color: #473BF0;
        }
        .update-details {
            background-color: #f8f9fa;
            border-radius: 6px;
            padding: 12px;
            border-left: 3px solid #007bff;
        }
        .changes-list {
            margin-top: 8px;
        }
        .change-item {
            margin-bottom: 8px;
            padding: 6px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .change-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        .change-values {
            margin-top: 4px;
            display: flex;
            align-items: center;
            flex-wrap: wrap;
        }
        .old-value {
            color: #dc3545;
            text-decoration: line-through;
            background-color: #f8d7da;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 0.9em;
        }
        .new-value {
            color: #28a745;
            font-weight: bold;
            background-color: #d4edda;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 0.9em;
        }
        .badge-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .badge-success {
            background-color: #28a745;
            color: #fff;
        }
        @media (max-width: 768px) {
            .change-values {
                flex-direction: column;
                align-items: flex-start;
            }
            .change-values .mx-2 {
                margin: 4px 0 !important;
                transform: rotate(90deg);
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1><i class="fas fa-sync-alt text-primary"></i> Appika Customer Updates Log</h1>
            <div class="admin-user">
                <span>Welcome, <?php echo htmlspecialchars($admin_username); ?></span>
                <a href="admin-logout.php" class="btn btn-outline-danger btn-sm">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </div>
        </div>

        <!-- Time Filter Tabs -->
        <?php if ($columns_exist): ?>
        <div class="filter-tabs">
            <ul class="nav nav-pills">
                <li class="nav-item">
                    <a class="nav-link <?php echo $timeFilter === '1h' ? 'active' : ''; ?>"
                       href="?time=1h<?php echo !empty($search) ? '&search='.urlencode($search) : ''; ?>">Last Hour</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $timeFilter === '24h' ? 'active' : ''; ?>"
                       href="?time=24h<?php echo !empty($search) ? '&search='.urlencode($search) : ''; ?>">Last 24 Hours</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $timeFilter === '7d' ? 'active' : ''; ?>"
                       href="?time=7d<?php echo !empty($search) ? '&search='.urlencode($search) : ''; ?>">Last 7 Days</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $timeFilter === '30d' ? 'active' : ''; ?>"
                       href="?time=30d<?php echo !empty($search) ? '&search='.urlencode($search) : ''; ?>">Last 30 Days</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $timeFilter === 'all' ? 'active' : ''; ?>"
                       href="?time=all<?php echo !empty($search) ? '&search='.urlencode($search) : ''; ?>">All Time</a>
                </li>
            </ul>
        </div>
        <?php endif; ?>

        <div class="admin-content">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <a href="admin-users.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Users
                    </a>
                </div>
                <div>
                    <form method="GET" class="form-inline">
                        <?php if (!empty($timeFilter) && $timeFilter !== '24h'): ?>
                        <input type="hidden" name="time" value="<?php echo htmlspecialchars($timeFilter); ?>">
                        <?php endif; ?>
                        <div class="input-group">
                            <input type="text" name="search" class="form-control"
                                   placeholder="Search customers..."
                                   value="<?php echo htmlspecialchars($search); ?>">
                            <div class="input-group-append">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <?php if (!$columns_exist): ?>
            <div class="setup-notice">
                <i class="fas fa-exclamation-triangle text-warning mb-3" style="font-size: 2em;"></i>
                <h4>Setup Required</h4>
                <p>Appika customer sync is not set up yet. Run the setup script to enable automatic updates from Appika.</p>
                <a href="../setup/setup-customer-sync.php" class="btn btn-warning">
                    <i class="fas fa-cog"></i> Run Setup
                </a>
            </div>
            <?php elseif (empty($updates)): ?>
            <div class="no-updates">
                <i class="fas fa-info-circle text-info mb-3" style="font-size: 2em;"></i>
                <h4>No Customer Updates Found</h4>
                <p>No customers have been updated from Appika yet<?php echo !empty($search) ? ' matching your search' : ''; ?>.</p>
                <?php if (!empty($search)): ?>
                <a href="appika-customer-updates-log.php" class="btn btn-outline-primary">
                    <i class="fas fa-list"></i> View All Updates
                </a>
                <?php endif; ?>
            </div>
            <?php else: ?>
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="mb-0">
                    <i class="fas fa-list text-primary"></i>
                    Customer Updates Log
                    <span class="badge badge-secondary"><?php echo number_format($total_count); ?> total</span>
                </h5>
                <?php if ($total_count > 0): ?>
                    <small class="text-muted">
                        Showing <?php echo $offset + 1; ?>-<?php echo min($offset + $items_per_page, $total_count); ?> of <?php echo number_format($total_count); ?>
                        <?php if (!empty($search)): ?>
                        (filtered by "<?php echo htmlspecialchars($search); ?>")
                        <?php endif; ?>
                    </small>
                <?php endif; ?>
            </div>

            <?php foreach ($updates as $update): ?>
            <div class="update-item">
                <div class="update-header">
                    <div class="customer-info">
                        <h6 class="mb-1">
                            <a href="admin-user-detail.php?id=<?php echo $update['id']; ?>" class="text-decoration-none">
                                <strong><?php echo htmlspecialchars($update['username']); ?></strong>
                            </a>
                            <?php if (!empty($update['appika_id'])): ?>
                            <span class="badge badge-info ml-2"><?php echo htmlspecialchars($update['appika_id']); ?></span>
                            <?php endif; ?>
                            <?php if (!empty($update['sync_operation'])): ?>
                            <span class="badge badge-<?php echo $update['sync_operation'] == 'simulation' ? 'warning' : 'success'; ?> ml-1">
                                <?php echo $update['sync_operation'] == 'simulation' ? 'Simulated' : ucfirst($update['sync_operation']); ?>
                            </span>
                            <?php endif; ?>
                        </h6>
                        <div class="text-muted">
                            <?php
                            $fullName = trim($update['first_name'] . ' ' . $update['last_name']);
                            if (!empty($fullName)) {
                                echo htmlspecialchars($fullName) . ' • ';
                            }
                            echo htmlspecialchars($update['email']);
                            if (!empty($update['company_name'])) {
                                echo ' • ' . htmlspecialchars($update['company_name']);
                            }
                            ?>
                        </div>
                    </div>
                    <div class="update-time">
                        <i class="fas fa-external-link-alt text-muted"></i>
                        <small>from Appika</small><br>
                        <small><?php echo timeAgo($update['appika_updated_at']); ?></small>
                    </div>
                </div>

                <?php if (!empty($update['changes'])): ?>
                <div class="update-details mt-3">
                    <h6 class="text-primary mb-2">
                        <i class="fas fa-edit"></i> Changes Made:
                    </h6>
                    <?php
                    $changes = json_decode($update['changes'], true);
                    if ($changes && is_array($changes)):
                    ?>
                    <div class="changes-list">
                        <?php foreach ($changes as $field => $change): ?>
                        <div class="change-item">
                            <strong><?php echo ucfirst(str_replace('_', ' ', $field)); ?>:</strong>
                            <div class="change-values">
                                <span class="old-value"><?php echo htmlspecialchars($change['old'] ?: 'Empty'); ?></span>
                                <i class="fas fa-arrow-right mx-2 text-muted"></i>
                                <span class="new-value"><?php echo htmlspecialchars($change['new']); ?></span>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <?php endif; ?>
                </div>
                <?php elseif (!empty($update['sync_message'])): ?>
                <div class="update-details mt-3">
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i> <?php echo htmlspecialchars($update['sync_message']); ?>
                    </small>
                </div>
                <?php endif; ?>
            </div>
            <?php endforeach; ?>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
            <nav aria-label="Customer updates pagination">
                <ul class="pagination justify-content-center">
                    <?php if ($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?php echo $page-1; ?>&time=<?php echo $timeFilter; ?><?php echo !empty($search) ? '&search='.urlencode($search) : ''; ?>">
                            <i class="fas fa-chevron-left"></i> Previous
                        </a>
                    </li>
                    <?php endif; ?>

                    <?php for ($i = max(1, $page-2); $i <= min($total_pages, $page+2); $i++): ?>
                    <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                        <a class="page-link" href="?page=<?php echo $i; ?>&time=<?php echo $timeFilter; ?><?php echo !empty($search) ? '&search='.urlencode($search) : ''; ?>">
                            <?php echo $i; ?>
                        </a>
                    </li>
                    <?php endfor; ?>

                    <?php if ($page < $total_pages): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?php echo $page+1; ?>&time=<?php echo $timeFilter; ?><?php echo !empty($search) ? '&search='.urlencode($search) : ''; ?>">
                            Next <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </nav>
            <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
