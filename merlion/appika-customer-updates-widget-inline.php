<?php
/**
 * Appika Customer Updates Widget (Inline Version)
 * Shows recent customers updated from Appika
 * Include this in admin-users.php
 */

// Check if required columns exist
function checkCustomerAppikaColumnsExist() {
    global $conn;

    $result = mysqli_query($conn, "SHOW COLUMNS FROM user LIKE 'appika_updated_at'");
    return mysqli_num_rows($result) > 0;
}

// Function to get recent Appika customer updates with detailed changes
function getRecentCustomerAppikaUpdates($limit = 10) {
    global $conn;

    // Check if columns exist first
    if (!checkCustomerAppikaColumnsExist()) {
        return [];
    }

    $query = "SELECT u.id, u.username, u.email, u.first_name, u.company_name,
                     u.appika_id, u.appika_customer_id, u.appika_updated_at, u.appika_update_source,
                     csl.changes, csl.message as sync_message, csl.operation as sync_operation
              FROM user u
              LEFT JOIN customer_sync_logs csl ON u.id = csl.user_id
                  AND csl.created_at = (
                      SELECT MAX(created_at)
                      FROM customer_sync_logs
                      WHERE user_id = u.id AND success = 1
                  )
              WHERE u.appika_updated_at IS NOT NULL
              ORDER BY u.appika_updated_at DESC
              LIMIT ?";

    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'i', $limit);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    $updates = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $updates[] = $row;
    }

    return $updates;
}

// Function to count customer updates in last 24 hours
function countRecentCustomerAppikaUpdates() {
    global $conn;

    // Check if columns exist first
    if (!checkCustomerAppikaColumnsExist()) {
        return 0;
    }

    $query = "SELECT COUNT(*) as count
              FROM user
              WHERE appika_updated_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)";

    $result = mysqli_query($conn, $query);
    $row = mysqli_fetch_assoc($result);

    return $row['count'];
}

// Function to format time ago
function customerTimeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . ' min ago';
    if ($time < 86400) return floor($time/3600) . ' hr ago';
    if ($time < 2592000) return floor($time/86400) . ' days ago';
    
    return date('M j, Y', strtotime($datetime));
}

// Get recent customer updates
$recentCustomerUpdates = getRecentCustomerAppikaUpdates(2);
$customerUpdateCount24h = countRecentCustomerAppikaUpdates();
$customerColumnsExist = checkCustomerAppikaColumnsExist();
?>

<div class="appika-customer-updates-widget">
    <div class="widget-header">
        <h5>
            <i class="fas fa-sync-alt text-primary"></i>
            Appika Updates
            <?php if ($customerUpdateCount24h > 0): ?>
            <span class="badge badge-primary"><?php echo $customerUpdateCount24h; ?></span>
            <?php endif; ?>
        </h5>
        <small class="text-muted">Last 24 hours</small>
    </div>

    <div class="widget-content">
        <?php if (!$customerColumnsExist): ?>
        <div class="setup-notice">
            <i class="fas fa-exclamation-triangle text-warning"></i>
            <p class="mb-2"><strong>Setup Required</strong></p>
            <p class="mb-2">Appika customer sync is not set up yet. Run the setup script to enable automatic updates from
                Appika.</p>
            <a href="../setup/setup-customer-sync.php" class="btn btn-sm btn-warning">
                <i class="fas fa-cog"></i> Run Setup
            </a>
        </div>
        <?php elseif (empty($recentCustomerUpdates)): ?>
        <div class="no-updates">
            <i class="fas fa-check-circle text-success"></i>
            <p class="mb-0">No recent customer updates from Appika</p>
        </div>
        <?php else: ?>
        <div class="updates-list">
            <?php foreach ($recentCustomerUpdates as $update): ?>
            <div class="update-item">
                <div class="update-info">
                    <div class="update-header">
                        <a href="admin-user-detail.php?id=<?php echo $update['id']; ?>" class="customer-link">
                            <strong><?php echo htmlspecialchars($update['username']); ?></strong>
                            <?php if (!empty($update['appika_id'])): ?>
                            <span class="badge badge-info badge-sm"><?php echo htmlspecialchars($update['appika_id']); ?></span>
                            <?php endif; ?>
                        </a>
                        <span class="update-time"><?php echo customerTimeAgo($update['appika_updated_at']); ?></span>
                    </div>

                    <div class="update-details">
                        <div class="customer-name">
                            <?php
                            $fullName = trim($update['first_name'] . ' ' . $update['last_name']);
                            if (!empty($fullName)) {
                                echo htmlspecialchars($fullName);
                            } else {
                                echo htmlspecialchars($update['email']);
                            }
                            ?>
                            <?php if (!empty($update['sync_operation'])): ?>
                            <span class="badge badge-<?php echo $update['sync_operation'] == 'simulation' ? 'warning' : 'success'; ?> badge-sm ml-1">
                                <?php echo $update['sync_operation'] == 'simulation' ? 'Simulated' : ucfirst($update['sync_operation']); ?>
                            </span>
                            <?php endif; ?>
                        </div>
                        <div class="customer-meta">
                            <?php if (!empty($update['company_name'])): ?>
                            <span class="badge badge-secondary"><?php echo htmlspecialchars(substr($update['company_name'], 0, 20)) . (strlen($update['company_name']) > 20 ? '...' : ''); ?></span>
                            <?php endif; ?>
                            <small class="text-muted"><?php echo htmlspecialchars($update['email']); ?></small>
                        </div>

                        <?php if (!empty($update['changes'])): ?>
                        <div class="changes-summary">
                            <?php
                            $changes = json_decode($update['changes'], true);
                            if ($changes && is_array($changes)):
                                $changeFields = array_keys($changes);
                                $changeCount = count($changeFields);
                                if ($changeCount > 0):
                            ?>
                            <small class="text-primary">
                                <i class="fas fa-edit"></i>
                                <?php if ($changeCount == 1): ?>
                                    Updated <?php echo ucfirst(str_replace('_', ' ', $changeFields[0])); ?>
                                <?php elseif ($changeCount == 2): ?>
                                    Updated <?php echo ucfirst(str_replace('_', ' ', $changeFields[0])); ?> and <?php echo ucfirst(str_replace('_', ' ', $changeFields[1])); ?>
                                <?php else: ?>
                                    Updated <?php echo $changeCount; ?> fields: <?php echo implode(', ', array_map(function($f) { return ucfirst(str_replace('_', ' ', $f)); }, array_slice($changeFields, 0, 2))); ?><?php echo $changeCount > 2 ? '...' : ''; ?>
                                <?php endif; ?>
                            </small>
                            <?php endif; ?>
                            <?php endif; ?>
                        </div>
                        <?php elseif (!empty($update['sync_message'])): ?>
                        <div class="sync-message">
                            <small class="text-muted">
                                <i class="fas fa-info-circle"></i> <?php echo htmlspecialchars($update['sync_message']); ?>
                            </small>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="update-source">
                    <i class="fas fa-external-link-alt text-muted"></i>
                    <small class="text-muted">from Appika</small>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <div class="widget-footer">
            <a href="appika-customer-updates-log.php" class="btn btn-sm btn-outline-primary">
                <i class="fas fa-list"></i> &nbsp; View All Updates
            </a>
            <a href="simulate-appika-customer-update.php" class="btn btn-sm btn-outline-info ml-2" target="_blank"
                style="margin-left:8px;">
                <i class="fas fa-flask"></i> &nbsp; Simulate Appika Update
            </a>
        </div>
        <?php endif; ?>
    </div>
</div>

<style>
.appika-customer-updates-widget {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.appika-customer-updates-widget .widget-header {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.appika-customer-updates-widget .widget-header h5 {
    margin: 0;
    font-size: 16px;
    color: #333;
}

.appika-customer-updates-widget .widget-content {
    padding: 15px 20px;
}

.appika-customer-updates-widget .no-updates {
    text-align: center;
    padding: 20px;
    color: #666;
}

.appika-customer-updates-widget .setup-notice {
    text-align: center;
    padding: 20px;
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    color: #856404;
}

.appika-customer-updates-widget .update-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 12px 0;
    border-bottom: 1px solid #f5f5f5;
}

.appika-customer-updates-widget .update-item:last-child {
    border-bottom: none;
}

.appika-customer-updates-widget .update-info {
    flex: 1;
}

.appika-customer-updates-widget .update-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.appika-customer-updates-widget .customer-link {
    text-decoration: none;
    color: #007bff;
    font-weight: 500;
}

.appika-customer-updates-widget .customer-link:hover {
    text-decoration: underline;
}

.appika-customer-updates-widget .update-time {
    font-size: 12px;
    color: #666;
}

.appika-customer-updates-widget .customer-name {
    font-size: 14px;
    color: #333;
    margin-bottom: 5px;
}

.appika-customer-updates-widget .customer-meta {
    display: flex;
    align-items: center;
    gap: 8px;
}

.appika-customer-updates-widget .badge-sm {
    font-size: 10px;
    padding: 2px 6px;
}

.appika-customer-updates-widget .update-source {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-left: 15px;
}

.appika-customer-updates-widget .widget-footer {
    padding: 10px 20px;
    border-top: 1px solid #eee;
    text-align: center;
}

/* Badge colors */
.appika-customer-updates-widget .badge-info {
    background-color: #17a2b8;
    color: #fff;
    font-family: 'Courier New', monospace;
    font-weight: bold;
    font-size: 11px;
    padding: 3px 6px;
}

.appika-customer-updates-widget .badge-primary {
    background-color: #473BF0;
    color: #fff;
}

.appika-customer-updates-widget .badge-secondary {
    background-color: #6c757d;
    color: #fff;
}

.appika-customer-updates-widget .badge-warning {
    background-color: #ffc107;
    color: #212529;
}

.appika-customer-updates-widget .badge-success {
    background-color: #28a745;
    color: #fff;
}

.appika-customer-updates-widget .changes-summary {
    margin-top: 5px;
}

.appika-customer-updates-widget .sync-message {
    margin-top: 5px;
}

/* Responsive */
@media (max-width: 768px) {
    .appika-customer-updates-widget .widget-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .appika-customer-updates-widget .update-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .appika-customer-updates-widget .update-time {
        margin-top: 5px;
    }

    .appika-customer-updates-widget .customer-meta {
        flex-wrap: wrap;
    }
}
</style>
