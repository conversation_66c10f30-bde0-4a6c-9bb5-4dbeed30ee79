<?php
/**
 * Appika Reverse Sync - Sync updates FROM Appika TO local database
 * This handles automatic updates when admins update tickets in Appika
 */

require_once 'graphql_functions.php';
require_once 'appika_sync.php';

/**
 * Check for updates from Appika and sync to local database
 * @param int $localTicketId Local ticket ID
 * @return array Result of sync operation
 */
function syncFromAppika($localTicketId) {
    global $conn;
    
    $result = [
        'success' => false,
        'updated' => false,
        'message' => '',
        'changes' => []
    ];
    
    try {
        // Get local ticket data
        $localTicket = getLocalTicketData($localTicketId);
        if (!$localTicket) {
            $result['message'] = 'Local ticket not found';
            return $result;
        }
        
        // Skip if no Appika ID
        if (empty($localTicket['appika_id'])) {
            $result['success'] = true;
            $result['message'] = 'No Appika ID - skipping sync';
            return $result;
        }
        
        // Get Appika ticket data
        $appikaTicket = fetchAppikaTicketData($localTicket['appika_id']);
        if (!$appikaTicket) {
            $result['message'] = 'Failed to fetch Appika ticket data';
            return $result;
        }
        
        // Check for changes and update if needed
        $changes = detectChanges($localTicket, $appikaTicket);
        if (!empty($changes)) {
            $updateResult = applyChangesToLocal($localTicketId, $changes);
            if ($updateResult) {
                $result['success'] = true;
                $result['updated'] = true;
                $result['changes'] = $changes;
                $result['message'] = 'Ticket updated from Appika: ' . implode(', ', array_keys($changes));
                
                // Log the reverse sync
                logAppikaSync($localTicketId, 'reverse_sync', $result);
            } else {
                $result['message'] = 'Failed to apply changes to local database';
            }
        } else {
            $result['success'] = true;
            $result['message'] = 'No changes detected';
        }
        
    } catch (Exception $e) {
        $result['message'] = 'Error during sync: ' . $e->getMessage();
        error_log("Appika reverse sync error for ticket $localTicketId: " . $e->getMessage());
    }
    
    return $result;
}

/**
 * Get local ticket data
 */
function getLocalTicketData($ticketId) {
    global $conn;
    
    $query = "SELECT id, appika_id, status, priority, updated_at 
              FROM support_tickets 
              WHERE id = ?";
    
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'i', $ticketId);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    return mysqli_fetch_assoc($result);
}

/**
 * Fetch ticket data from Appika API
 */
function fetchAppikaTicketData($appikaId) {
    // Extract numeric ID from appika_id (e.g., HT076 -> 76)
    $numericId = (int)filter_var($appikaId, FILTER_SANITIZE_NUMBER_INT);
    
    if ($numericId <= 0) {
        return null;
    }
    
    $query = '
    query GetTicket($id: Int!) {
        getTicket(id: $id) {
            id
            ticket_no
            status
            priority
            updated
        }
    }';
    
    $variables = ['id' => $numericId];
    $result = makeGraphQLRequest($query, $variables);
    
    if ($result['success'] && isset($result['data']['data']['getTicket'])) {
        return $result['data']['data']['getTicket'];
    }
    
    return null;
}

/**
 * Detect changes between local and Appika ticket data
 */
function detectChanges($localTicket, $appikaTicket) {
    $changes = [];
    
    // Map Appika data to local format
    $appikaStatusLocal = mapAppikaStatusToLocal($appikaTicket['status']);
    $appikaPriorityLocal = mapAppikaPriorityToLocal($appikaTicket['priority']);
    
    // Check for status changes
    if ($localTicket['status'] !== $appikaStatusLocal) {
        $changes['status'] = [
            'old' => $localTicket['status'],
            'new' => $appikaStatusLocal,
            'appika_value' => $appikaTicket['status']
        ];
    }
    
    // Check for priority changes
    if ($localTicket['priority'] !== $appikaPriorityLocal) {
        $changes['priority'] = [
            'old' => $localTicket['priority'],
            'new' => $appikaPriorityLocal,
            'appika_value' => $appikaTicket['priority']
        ];
    }
    
    return $changes;
}

/**
 * Apply changes to local database
 */
function applyChangesToLocal($ticketId, $changes) {
    global $conn;

    $updateFields = [];
    $updateValues = [];
    $updateTypes = '';

    foreach ($changes as $field => $change) {
        $updateFields[] = "$field = ?";
        $updateValues[] = $change['new'];
        $updateTypes .= 's';
    }

    if (empty($updateFields)) {
        return true; // No changes to apply
    }

    // Add Appika update tracking
    $updateFields[] = "updated_at = NOW()";
    $updateFields[] = "appika_updated_at = NOW()";
    $updateFields[] = "appika_update_source = ?";

    $updateValues[] = 'appika_admin'; // Source of update
    $updateTypes .= 's';

    $query = "UPDATE support_tickets SET " . implode(', ', $updateFields) . " WHERE id = ?";
    $updateValues[] = $ticketId;
    $updateTypes .= 'i';

    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, $updateTypes, ...$updateValues);

    return mysqli_stmt_execute($stmt);
}

/**
 * Map Appika status to local status
 */
function mapAppikaStatusToLocal($appikaStatus) {
    $statusMapping = [
        'OPEN' => 'open',
        'WIP' => 'in_progress',
        'CLOSED' => 'closed'
    ];

    return $statusMapping[strtoupper($appikaStatus)] ?? 'open';
}

/**
 * Map Appika priority to local priority
 */
function mapAppikaPriorityToLocal($appikaPriority) {
    $priorityMapping = [
        'LOW' => 'Information',      // LOW → Information (lowest priority)
        'MEDIUM' => 'Normal',        // MEDIUM → Normal (your standard priority)
        'HIGH' => 'Important',       // HIGH → Important
        'URGENT' => 'Critical'       // URGENT → Critical (highest priority)
    ];

    return $priorityMapping[strtoupper($appikaPriority)] ?? 'Normal';
}

/**
 * Bulk sync multiple tickets (for scheduled sync)
 */
function bulkSyncFromAppika($limit = 50) {
    global $conn;
    
    $results = [
        'total_checked' => 0,
        'total_updated' => 0,
        'errors' => []
    ];
    
    // Get tickets with Appika IDs that were updated recently or need checking
    $query = "SELECT id, appika_id 
              FROM support_tickets 
              WHERE appika_id IS NOT NULL 
              AND appika_id != ''
              AND (
                  updated_at >= DATE_SUB(NOW(), INTERVAL 2 HOUR)
                  OR last_appika_sync IS NULL
                  OR last_appika_sync <= DATE_SUB(NOW(), INTERVAL 1 HOUR)
              )
              ORDER BY updated_at DESC
              LIMIT ?";
    
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'i', $limit);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    while ($ticket = mysqli_fetch_assoc($result)) {
        $results['total_checked']++;
        
        $syncResult = syncFromAppika($ticket['id']);
        
        if ($syncResult['success']) {
            if ($syncResult['updated']) {
                $results['total_updated']++;
            }
            
            // Update last sync timestamp
            updateLastSyncTimestamp($ticket['id']);
        } else {
            $results['errors'][] = "Ticket {$ticket['id']}: " . $syncResult['message'];
        }
    }
    
    return $results;
}

/**
 * Update last sync timestamp
 */
function updateLastSyncTimestamp($ticketId) {
    global $conn;
    
    $query = "UPDATE support_tickets SET last_appika_sync = NOW() WHERE id = ?";
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'i', $ticketId);
    mysqli_stmt_execute($stmt);
}

/**
 * Check if ticket needs sync (called when viewing tickets)
 */
function shouldCheckForUpdates($ticketId) {
    global $conn;

    $query = "SELECT last_appika_sync, appika_id, updated_at, appika_updated_at
              FROM support_tickets
              WHERE id = ? AND appika_id IS NOT NULL AND appika_id != ''";

    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'i', $ticketId);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $ticket = mysqli_fetch_assoc($result);

    if (!$ticket) {
        return false; // No Appika ID
    }

    // Don't sync if ticket was recently updated locally (within 10 minutes)
    if (!empty($ticket['updated_at'])) {
        $lastLocalUpdate = strtotime($ticket['updated_at']);
        $lastAppikaUpdate = !empty($ticket['appika_updated_at']) ? strtotime($ticket['appika_updated_at']) : 0;
        $tenMinutesAgo = time() - (10 * 60);

        // If local update is more recent than Appika update AND within last 10 minutes
        if ($lastLocalUpdate > $lastAppikaUpdate && $lastLocalUpdate > $tenMinutesAgo) {
            return false; // Skip sync to avoid overwriting recent local changes
        }
    }

    // Check if last sync was more than 5 minutes ago or never synced
    if (empty($ticket['last_appika_sync'])) {
        return true;
    }

    $lastSync = strtotime($ticket['last_appika_sync']);
    $fiveMinutesAgo = time() - (5 * 60);

    return $lastSync < $fiveMinutesAgo;
}
?>
