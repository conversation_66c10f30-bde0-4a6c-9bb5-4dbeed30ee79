<?php
/**
 * Appika Customer Reverse Sync Functions
 * Handles syncing customer data FROM Appika TO local database
 */

require_once '../config/api-config.php';
require_once '../vendor/autoload.php';

/**
 * Bulk sync multiple customers (for scheduled sync)
 */
function bulkSyncCustomersFromAppika($limit = 50) {
    global $conn;
    
    $results = [
        'total_checked' => 0,
        'total_updated' => 0,
        'errors' => []
    ];
    
    // Get users with Appika customer IDs that were updated recently or need checking
    $query = "SELECT id, appika_customer_id, first_name, last_name, email 
              FROM user 
              WHERE appika_customer_id IS NOT NULL 
              AND appika_customer_id != ''
              AND (
                  updated_at >= DATE_SUB(NOW(), INTERVAL 2 HOUR)
                  OR last_appika_sync IS NULL
                  OR last_appika_sync <= DATE_SUB(NOW(), INTERVAL 1 HOUR)
              )
              ORDER BY updated_at DESC
              LIMIT ?";
    
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'i', $limit);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    while ($user = mysqli_fetch_assoc($result)) {
        $results['total_checked']++;
        
        try {
            // Sync this customer
            $syncResult = syncSingleCustomerFromAppika($user['id']);
            
            if ($syncResult['updated']) {
                $results['total_updated']++;
            }
            
            // Update last sync time regardless of whether changes were made
            $updateSyncTime = "UPDATE user SET last_appika_sync = NOW() WHERE id = ?";
            $updateStmt = mysqli_prepare($conn, $updateSyncTime);
            mysqli_stmt_bind_param($updateStmt, 'i', $user['id']);
            mysqli_stmt_execute($updateStmt);
            
        } catch (Exception $e) {
            $results['errors'][] = "User ID {$user['id']}: " . $e->getMessage();
        }
    }
    
    return $results;
}

/**
 * Sync single customer from Appika
 */
function syncSingleCustomerFromAppika($userId) {
    global $conn;
    
    $result = [
        'success' => false,
        'updated' => false,
        'message' => '',
        'changes' => []
    ];
    
    try {
        // Get local user data
        $localUser = getLocalCustomerData($userId);
        if (!$localUser) {
            $result['message'] = 'Local user not found';
            return $result;
        }
        
        // Skip if no Appika customer ID
        if (empty($localUser['appika_customer_id'])) {
            $result['success'] = true;
            $result['message'] = 'No Appika customer ID - skipping sync';
            return $result;
        }
        
        // Get Appika customer data
        $appikaCustomer = fetchAppikaCustomerById($localUser['appika_customer_id']);
        if (!$appikaCustomer) {
            $result['message'] = 'Failed to fetch Appika customer data';
            return $result;
        }
        
        // Check for changes and update if needed
        $changes = detectCustomerDataChanges($localUser, $appikaCustomer);
        $customerUpdated = false;

        if (!empty($changes)) {
            $updateResult = applyCustomerChangesToDatabase($userId, $changes);
            if ($updateResult) {
                $customerUpdated = true;
                $result['success'] = true;
                $result['updated'] = true;
                $result['changes'] = $changes;
                $result['message'] = 'Customer updated from Appika: ' . implode(', ', array_keys($changes));

                // Log the sync
                logCustomerSyncOperation($userId, 'bulk_sync', $result);
            } else {
                $result['message'] = 'Failed to apply changes to local database';
                return $result;
            }
        }

        // Also sync location data
        $locationSyncResult = syncCustomerLocationFromAppika($userId);
        if ($locationSyncResult['updated']) {
            $customerUpdated = true;
            if (!empty($result['changes'])) {
                $result['changes'] = array_merge($result['changes'], $locationSyncResult['changes']);
            } else {
                $result['changes'] = $locationSyncResult['changes'];
            }

            if ($result['updated']) {
                $result['message'] .= ' and location: ' . implode(', ', array_keys($locationSyncResult['changes']));
            } else {
                $result['message'] = 'Location updated from Appika: ' . implode(', ', array_keys($locationSyncResult['changes']));
                $result['updated'] = true;
                $result['success'] = true;
            }
        }

        if (!$customerUpdated) {
            $result['success'] = true;
            $result['message'] = 'No changes detected';
        }
        
    } catch (Exception $e) {
        $result['message'] = 'Error during sync: ' . $e->getMessage();
        error_log("Appika customer sync error for user $userId: " . $e->getMessage());
    }
    
    return $result;
}

/**
 * Get local customer data
 */
function getLocalCustomerData($userId) {
    global $conn;

    $query = "SELECT id, appika_customer_id, first_name, email, company_name, tell,
                     address, address2, city, state, country, postal_code, updated_at, appika_updated_at
              FROM user
              WHERE id = ?";

    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'i', $userId);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    return mysqli_fetch_assoc($result);
}

/**
 * Fetch customer data from Appika API
 */
function fetchAppikaCustomerById($appikaCustomerId) {
    $apiConfig = getCustomerApiConfig();
    $apiEndpoint = $apiConfig['endpoint'];
    $apiPath = $apiConfig['path'];
    $apiKey = $apiConfig['key'];
    
    $client = new \GuzzleHttp\Client([
        'base_uri' => $apiEndpoint,
        'timeout' => 30,
        'http_errors' => false,
    ]);
    
    try {
        $response = $client->request('GET', $apiPath . '/' . $appikaCustomerId, [
            'headers' => [
                'Authorization' => "Bearer {$apiKey}",
                'Accept' => 'application/json',
            ]
        ]);
        
        $statusCode = $response->getStatusCode();
        $body = $response->getBody()->getContents();
        
        if ($statusCode >= 200 && $statusCode < 300) {
            return json_decode($body, true);
        }
        
    } catch (Exception $e) {
        error_log("Error fetching Appika customer data: " . $e->getMessage());
    }
    
    return null;
}

/**
 * Detect changes between local and Appika customer data
 */
function detectCustomerDataChanges($localUser, $appikaCustomer) {
    $changes = [];

    // Check for name changes - now only using first_name for full name
    $localFullName = trim($localUser['first_name']);
    $appikaName = $appikaCustomer['name'] ?? '';

    if ($localFullName !== $appikaName && !empty($appikaName)) {
        // Put full Appika name in first_name
        $appikaFirstName = $appikaName;  // Full name goes to first_name

        // Update if there are actual changes
        if ($localUser['first_name'] !== $appikaFirstName) {
            $changes['first_name'] = [
                'old' => $localUser['first_name'],
                'new' => $appikaFirstName,
                'appika_value' => $appikaName
            ];
        }
    }

    // Check for email changes (if Appika has email field)
    if (isset($appikaCustomer['email']) && !empty($appikaCustomer['email'])) {
        $appikaEmail = $appikaCustomer['email'];
        if ($localUser['email'] !== $appikaEmail) {
            $changes['email'] = [
                'old' => $localUser['email'],
                'new' => $appikaEmail,
                'appika_value' => $appikaEmail
            ];
        }
    }

    // Note: Phone sync is handled in location sync since Appika stores phone in location data

    // Check for company name changes (if Appika has company field)
    if (isset($appikaCustomer['company']) && !empty($appikaCustomer['company'])) {
        $appikaCompany = $appikaCustomer['company'];
        if ($localUser['company_name'] !== $appikaCompany) {
            $changes['company_name'] = [
                'old' => $localUser['company_name'],
                'new' => $appikaCompany,
                'appika_value' => $appikaCompany
            ];
        }
    }

    return $changes;
}

/**
 * Sync customer location/address data from Appika
 */
function syncCustomerLocationFromAppika($userId) {
    global $conn;

    $result = [
        'success' => false,
        'updated' => false,
        'message' => '',
        'changes' => []
    ];

    try {
        // Get local user data
        $localUser = getLocalCustomerData($userId);
        if (!$localUser || empty($localUser['appika_customer_id'])) {
            $result['message'] = 'No Appika customer ID found';
            return $result;
        }

        // Get Appika customer locations
        $appikaLocations = fetchAppikaCustomerLocations($localUser['appika_customer_id']);
        if (!$appikaLocations) {
            $result['message'] = 'Failed to fetch Appika location data';
            return $result;
        }

        // Find primary location from Appika
        $primaryLocation = null;
        foreach ($appikaLocations as $location) {
            if (isset($location['is_primary_loc']) && $location['is_primary_loc'] === 'y') {
                $primaryLocation = $location;
                break;
            }
        }

        // If no primary location found, use the first location
        if (!$primaryLocation && !empty($appikaLocations)) {
            $primaryLocation = $appikaLocations[0];
        }

        if (!$primaryLocation) {
            $result['success'] = true;
            $result['message'] = 'No location data found in Appika';
            return $result;
        }

        // Check for location changes and update if needed
        $locationChanges = detectLocationChanges($localUser, $primaryLocation);
        if (!empty($locationChanges)) {
            $updateResult = applyLocationChangesToDatabase($userId, $locationChanges);
            if ($updateResult) {
                $result['success'] = true;
                $result['updated'] = true;
                $result['changes'] = $locationChanges;
                $result['message'] = 'Location updated from Appika: ' . implode(', ', array_keys($locationChanges));

                // Log the location sync
                logCustomerSyncOperation($userId, 'location_sync', $result);
            } else {
                $result['message'] = 'Failed to apply location changes to local database';
            }
        } else {
            $result['success'] = true;
            $result['message'] = 'No location changes detected';
        }

    } catch (Exception $e) {
        $result['message'] = 'Error during location sync: ' . $e->getMessage();
        error_log("Appika location sync error for user $userId: " . $e->getMessage());
    }

    return $result;
}

/**
 * Apply changes to local database
 */
function applyCustomerChangesToDatabase($userId, $changes) {
    global $conn;

    $updateFields = [];
    $updateValues = [];
    $updateTypes = '';

    foreach ($changes as $field => $change) {
        $updateFields[] = "$field = ?";
        $updateValues[] = $change['new'];
        $updateTypes .= 's';
    }

    if (empty($updateFields)) {
        return true; // No changes to apply
    }

    // Add Appika update tracking
    $updateFields[] = "updated_at = NOW()";
    $updateFields[] = "appika_updated_at = NOW()";
    $updateFields[] = "appika_update_source = ?";

    $updateValues[] = 'appika_bulk_sync'; // Source of update
    $updateTypes .= 's';

    $query = "UPDATE user SET " . implode(', ', $updateFields) . " WHERE id = ?";
    $updateValues[] = $userId;
    $updateTypes .= 'i';

    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, $updateTypes, ...$updateValues);

    return mysqli_stmt_execute($stmt);
}

/**
 * Log customer sync operation
 */
function logCustomerSyncOperation($userId, $operation, $result) {
    global $conn;
    
    $logData = [
        'user_id' => $userId,
        'operation' => $operation,
        'success' => $result['success'] ? 1 : 0,
        'message' => $result['message'],
        'changes' => !empty($result['changes']) ? json_encode($result['changes']) : null,
        'created_at' => date('Y-m-d H:i:s')
    ];
    
    // Create customer_sync_logs table if it doesn't exist
    $createTableQuery = "CREATE TABLE IF NOT EXISTS customer_sync_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        operation VARCHAR(50) NOT NULL,
        success TINYINT(1) NOT NULL,
        message TEXT,
        changes JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_created_at (created_at)
    )";
    
    mysqli_query($conn, $createTableQuery);
    
    // Insert log entry
    $insertQuery = "INSERT INTO customer_sync_logs (user_id, operation, success, message, changes, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?)";
    
    $stmt = mysqli_prepare($conn, $insertQuery);
    mysqli_stmt_bind_param($stmt, 'isisss', 
        $logData['user_id'], 
        $logData['operation'], 
        $logData['success'], 
        $logData['message'], 
        $logData['changes'], 
        $logData['created_at']
    );
    
    mysqli_stmt_execute($stmt);
}

/**
 * Fetch customer locations from Appika API
 */
function fetchAppikaCustomerLocations($appikaCustomerId) {
    $apiConfig = getCustomerApiConfig();
    $apiEndpoint = $apiConfig['endpoint'];
    $apiPath = $apiConfig['path'];
    $apiKey = $apiConfig['key'];

    $client = new \GuzzleHttp\Client([
        'base_uri' => $apiEndpoint,
        'timeout' => 30,
        'http_errors' => false,
    ]);

    try {
        $response = $client->request('GET', $apiPath . '/' . $appikaCustomerId . '/locations', [
            'headers' => [
                'Authorization' => "Bearer {$apiKey}",
                'Accept' => 'application/json',
            ]
        ]);

        $statusCode = $response->getStatusCode();
        $body = $response->getBody()->getContents();

        if ($statusCode >= 200 && $statusCode < 300) {
            $data = json_decode($body, true);
            return $data['items'] ?? [];
        }

    } catch (Exception $e) {
        error_log("Error fetching Appika customer locations: " . $e->getMessage());
    }

    return null;
}

/**
 * Detect changes between local and Appika location data
 */
function detectLocationChanges($localUser, $appikaLocation) {
    $changes = [];

    // Check for address changes
    if (isset($appikaLocation['add1']) && !empty($appikaLocation['add1'])) {
        $appikaAddress = $appikaLocation['add1'];
        if ($localUser['address'] !== $appikaAddress) {
            $changes['address'] = [
                'old' => $localUser['address'],
                'new' => $appikaAddress,
                'appika_value' => $appikaAddress
            ];
        }
    }

    // Check for address2 changes
    if (isset($appikaLocation['add2']) && !empty($appikaLocation['add2'])) {
        $appikaAddress2 = $appikaLocation['add2'];
        if ($localUser['address2'] !== $appikaAddress2) {
            $changes['address2'] = [
                'old' => $localUser['address2'],
                'new' => $appikaAddress2,
                'appika_value' => $appikaAddress2
            ];
        }
    }

    // Check for city changes
    if (isset($appikaLocation['city']) && !empty($appikaLocation['city'])) {
        $appikaCity = $appikaLocation['city'];
        if ($localUser['city'] !== $appikaCity) {
            $changes['city'] = [
                'old' => $localUser['city'],
                'new' => $appikaCity,
                'appika_value' => $appikaCity
            ];
        }
    }

    // Check for state changes
    if (isset($appikaLocation['state_code']) && !empty($appikaLocation['state_code'])) {
        $appikaState = $appikaLocation['state_code'];
        if ($localUser['state'] !== $appikaState) {
            $changes['state'] = [
                'old' => $localUser['state'],
                'new' => $appikaState,
                'appika_value' => $appikaState
            ];
        }
    }

    // Check for postal code changes
    if (isset($appikaLocation['zip']) && !empty($appikaLocation['zip'])) {
        $appikaZip = $appikaLocation['zip'];
        if ($localUser['postal_code'] !== $appikaZip) {
            $changes['postal_code'] = [
                'old' => $localUser['postal_code'],
                'new' => $appikaZip,
                'appika_value' => $appikaZip
            ];
        }
    }

    // Check for country changes
    if (isset($appikaLocation['ccode']) && !empty($appikaLocation['ccode'])) {
        $appikaCountry = $appikaLocation['ccode'];
        if ($localUser['country'] !== $appikaCountry) {
            $changes['country'] = [
                'old' => $localUser['country'],
                'new' => $appikaCountry,
                'appika_value' => $appikaCountry
            ];
        }
    }

    // Check for phone changes from location data (tel_work is primary phone)
    if (isset($appikaLocation['tel_work']) && !empty($appikaLocation['tel_work'])) {
        $appikaPhone = $appikaLocation['tel_work'];
        if ($localUser['tell'] !== $appikaPhone) {
            $changes['tell'] = [
                'old' => $localUser['tell'],
                'new' => $appikaPhone,
                'appika_value' => $appikaPhone
            ];
        }
    }
    // Fallback to tel_alt if tel_work is empty
    elseif (isset($appikaLocation['tel_alt']) && !empty($appikaLocation['tel_alt'])) {
        $appikaPhone = $appikaLocation['tel_alt'];
        if ($localUser['tell'] !== $appikaPhone) {
            $changes['tell'] = [
                'old' => $localUser['tell'],
                'new' => $appikaPhone,
                'appika_value' => $appikaPhone
            ];
        }
    }

    return $changes;
}

/**
 * Apply location changes to local database
 */
function applyLocationChangesToDatabase($userId, $changes) {
    global $conn;

    $updateFields = [];
    $updateValues = [];
    $updateTypes = '';

    foreach ($changes as $field => $change) {
        $updateFields[] = "$field = ?";
        $updateValues[] = $change['new'];
        $updateTypes .= 's';
    }

    if (empty($updateFields)) {
        return true; // No changes to apply
    }

    // Add location update tracking
    $updateFields[] = "updated_at = NOW()";
    $updateFields[] = "appika_updated_at = NOW()";
    $updateFields[] = "appika_update_source = ?";

    $updateValues[] = 'appika_location_sync'; // Source of update
    $updateTypes .= 's';

    $query = "UPDATE user SET " . implode(', ', $updateFields) . " WHERE id = ?";
    $updateValues[] = $userId;
    $updateTypes .= 'i';

    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, $updateTypes, ...$updateValues);

    return mysqli_stmt_execute($stmt);
}

/**
 * Log customer sync activity (alias for compatibility)
 */
function logCustomerSyncActivity($userId, $operation, $success, $message, $changes = null) {
    $result = [
        'success' => $success,
        'message' => $message,
        'changes' => $changes
    ];

    return logCustomerSyncOperation($userId, $operation, $result);
}

?>
