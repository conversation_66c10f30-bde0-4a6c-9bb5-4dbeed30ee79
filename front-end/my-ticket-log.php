<?php
session_start();
include('../functions/server.php');

if (isset($_GET['logout'])) {
    session_destroy();
    unset($_SESSION['username']);
    header('location: ../index.php');
    exit();
}

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    header('location: sign-in.php');
    exit();
}

$username = $_SESSION['username'];

// Get user information
$sqlUser = "SELECT * FROM user WHERE username = '$username'";
$resultUser = mysqli_query($conn, $sqlUser);
$user = mysqli_fetch_assoc($resultUser);
$userId = $user['id'];

// Get search query from form
$search = isset($_GET['search']) ? trim($_GET['search']) : "";

// Search condition for ticket logs
$searchSql = "";
$searchSqlExpiration = "";
if (!empty($search)) {
    $searchSafe = mysqli_real_escape_string($conn, $search);
    $searchSql = "AND (
        tl.action LIKE '%$searchSafe%' OR
        tl.description LIKE '%$searchSafe%' OR
        tl.ticket_type LIKE '%$searchSafe%' OR
        DATE(tl.created_at) = '$searchSafe' OR
        YEAR(tl.created_at) = '$searchSafe' OR
        MONTH(tl.created_at) = '$searchSafe' OR
        DAY(tl.created_at) = '$searchSafe' OR
        DATE_FORMAT(tl.created_at, '%Y-%m') = '$searchSafe' OR
        DATE_FORMAT(tl.created_at, '%m-%d') = '$searchSafe'
    )";

    // Search condition for expiration logs
    $searchSqlExpiration = "AND (
        'expired' LIKE '%$searchSafe%' OR
        tel.ticket_type LIKE '%$searchSafe%' OR
        CONCAT('Expired ', tel.expired_quantity, ' ', tel.ticket_type, ' tickets') LIKE '%$searchSafe%' OR
        DATE(tel.cleanup_date) = '$searchSafe' OR
        YEAR(tel.cleanup_date) = '$searchSafe' OR
        MONTH(tel.cleanup_date) = '$searchSafe' OR
        DAY(tel.cleanup_date) = '$searchSafe' OR
        DATE_FORMAT(tel.cleanup_date, '%Y-%m') = '$searchSafe' OR
        DATE_FORMAT(tel.cleanup_date, '%m-%d') = '$searchSafe'
    )";
}

// Filters removed

// Pagination settings
$itemsPerPage = 5;
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
if ($page < 1) $page = 1;
$offset = ($page - 1) * $itemsPerPage;

// Filter condition to exclude purely technical API-related logs from user view
// This hides logs that are only relevant for system administrators
// but keeps user-relevant logs like ticket creation (even if they mention API status)
$apiFilterSql = "AND NOT (
    (tl.description LIKE '%API sync failed%' AND tl.description NOT LIKE '%Created new%ticket%') OR
    (tl.description LIKE '%Appika API%' AND tl.description NOT LIKE '%Created new%ticket%') OR
    (tl.description LIKE '%GraphQL%' AND tl.description NOT LIKE '%Created new%ticket%') OR
    (tl.description LIKE '%API error%' AND tl.description NOT LIKE '%Created new%ticket%') OR
    (tl.description LIKE '%API update%' AND tl.description NOT LIKE '%Created new%ticket%') OR
    (tl.description LIKE '%API failed%' AND tl.description NOT LIKE '%Created new%ticket%') OR
    (tl.description LIKE '%API response%' AND tl.description NOT LIKE '%Created new%ticket%')
)";

// Count total logs for pagination (including expiration logs)
$countSql = "SELECT COUNT(*) as total FROM (
    SELECT tl.created_at FROM ticket_logs tl
    WHERE tl.user_id = $userId $searchSql $apiFilterSql
    UNION ALL
    SELECT tel.cleanup_date as created_at FROM ticket_expiration_log tel
    WHERE tel.username = '$username' $searchSqlExpiration
) as combined_logs";
$countResult = mysqli_query($conn, $countSql);
$totalItems = mysqli_fetch_assoc($countResult)['total'];
$totalPages = ceil($totalItems / $itemsPerPage);

// Get combined ticket logs and expiration logs with pagination
$sqlLogs = "SELECT * FROM (
    SELECT
        tl.created_at,
        tl.action,
        tl.ticket_type,
        tl.amount,
        tl.description,
        tl.performed_by_admin_id,
        t.ticket_type as ticket_type_name,
        'ticket_log' as log_type
    FROM ticket_logs tl
    LEFT JOIN tickets t ON tl.ticket_id = t.ticketid
    WHERE tl.user_id = $userId $searchSql $apiFilterSql

    UNION ALL

    SELECT
        tel.cleanup_date as created_at,
        'expired' as action,
        tel.ticket_type,
        tel.expired_quantity as amount,
        CONCAT('Expired ', tel.expired_quantity, ' ', tel.ticket_type, ' tickets (purchased on ', DATE_FORMAT(tel.purchase_date, '%M %d, %Y'), ')') as description,
        NULL as performed_by_admin_id,
        tel.ticket_type as ticket_type_name,
        'expiration_log' as log_type
    FROM ticket_expiration_log tel
    WHERE tel.username = '$username' $searchSqlExpiration
) as combined_logs
ORDER BY created_at DESC
LIMIT $itemsPerPage OFFSET $offset";
$resultLogs = mysqli_query($conn, $sqlLogs);
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>My Ticket Logs</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap, fonts & icons -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Plugin's stylesheets -->
    <link rel="stylesheet" href="../plugins/aos/aos.min.css">
    <link rel="stylesheet" href="../plugins/fancybox/jquery.fancybox.min.css">
    <link rel="stylesheet" href="../plugins/nice-select/nice-select.min.css">
    <link rel="stylesheet" href="../plugins/slick/slick.min.css">
    <link rel="stylesheet" href="../plugins/date-picker/css/gijgo.min.css" type="text/css" />
    <!-- Vendor stylesheets -->
    <link rel="stylesheet" href="../plugins/theme-mode-switcher/switcher-panel.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/theme-mode-custom.css">
    <style>
    /* Additional CSS to ensure full page height */
    html,
    body {
        height: 100%;
        margin: 0;
    }

    body {
        margin-top: -120px !important;

    }

    @media (max-width: 767px) {
        body {
            margin-top: -100px !important;
        }

        .badge {
            font-size: 14px !important;
        }
    }

    .full-height {
        height: 100vh;
        /* Full viewport height */
    }

    .container {
        width: 1800px;
        max-width: 95%;
    }

    /* Make site-wrapper wider */
    .site-wrapper {
        max-width: 100% !important;
        width: 100% !important;
        overflow-x: visible !important;
    }

    /* Badge styles for different actions */
    .badge-purchase {
        background-color: #28a745;
        color: white;
    }

    .badge-refund {
        background-color: #dc3545;
        color: white;
    }

    .badge-usage {
        background-color: #17a2b8;
        color: white;
    }

    .badge-secondary {
        background-color: #6c757d;
        color: white;
    }

    .badge-updated {
        background-color: #17a2b8;
        color: white;
    }

    .badge-create {
        background-color: #6610f2;
        color: white;
    }

    .badge-update {
        background-color: #fd7e14;
        color: white;
    }

    /* Ticket type badges */
    .badge-starter {
        background-color: #fbbf24;
        color: #fff !important;
        font-size: 16px;
        padding: 6.8px;
    }

    .badge-premium {
        background-color: #01A7E1;
        color: #fff !important;
        font-size: 16px;
        padding: 6.8px;
    }

    .badge-ultimate {
        background-color: #793BF0;
        color: #fff !important;
        font-size: 16px;
        padding: 6.8px;
    }

    /* Badge general styling */
    .badge {
        padding: 6.8px;
        font-size: 16px;
        font-weight: 500;
        border-radius: 4px;
    }

    /* Ticket log status badges */
    .badge-success {
        background-color: #28a745;
        color: #fff;
    }

    .badge-pending {
        background-color: #ffc107;
        color: #212529;
    }

    .badge-fail {
        background-color: #dc3545;
        color: #fff;
    }

    .badge-cancel {
        background-color: #6c757d;
        color: #fff;
    }

    .badge-expired {
        background-color: #dc3545;
        color: #fff;
    }

    /* Table styles */
    .table {
        font-size: 0.95rem;
        color: #333;
    }

    .table th {
        font-weight: 600;
        background-color: #f8f9fa;
        border-bottom: 2px solid #dee2e6;
    }

    .table td {
        vertical-align: middle;
    }

    /* Card styles */
    .card h3 {
        color: #007bff;
        margin: 0;
        font-size: 1.25rem;
    }

    .card h5 {
        font-weight: 600;
        margin-bottom: 10px;
        font-size: 1rem;
    }

    /* Search bar styles to match my-ticket.php */
    .search-filter-row {
        padding: 0 40px;
    }

    .search-box {
        width: auto;
    }

    .search-box .input-group {
        width: 550px !important;
        max-width: 100% !important;
    }

    .search-input {
        border-top-right-radius: 0 !important;
        border-bottom-right-radius: 0 !important;
        height: 38px;
        font-size: 14px;
    }

    .search-button {
        border-top-right-radius: 4px !important;
        border-bottom-right-radius: 4px !important;
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0.375rem 0.75rem;
        width: 40px !important;
        min-width: 40px !important;
    }

    /* Responsive adjustments */
    @media (max-width: 991px) {
        .search-box {
            width: 100%;
        }

        .search-box .input-group {
            width: 100% !important;
            max-width: 500px !important;
            margin: 0 auto;
        }
    }

    @media (max-width: 767px) {
        .search-filter-row {
            padding: 0 15px;
        }

        .search-box .input-group {
            width: 100% !important;
            max-width: 100% !important;
        }

        .search-input {
            font-size: 16px;
            /* Larger font size for better mobile readability */
        }
    }

    /* Extra small devices */
    @media (max-width: 480px) {
        .search-filter-row {
            padding: 0 10px;
        }

        .search-input::placeholder {
            font-size: 14px;
        }
    }
    </style>
</head>

<body data-theme="light">

    <div class="site-wrapper overflow-hidden">
        <!-- Header Area -->
        <?php include('../header-footer/header.php'); ?>
        <!-- navbar-dark -->
        <?php if (isset($_SESSION['username'])) : ?>
        <div class="inner-banner pt-29 pb-md-13 bg-default-2">
            <div class="container"></div>
        </div>
        <div class="bg-default-2 pb-17 pb-md-29 ">
            <div class="container">
                <div class="row justify-content-md-between pt-9">
                    <div class="col-12 text-center mb-4">
                        <!-- Heading moved to inside the white card -->
                    </div>
                </div>

                <div class="row mt-4">
                    <!-- Left sidebar with user menu -->
                    <div class="col-lg-3 col-md-4">
                        <?php include('user-menu.php'); ?>
                    </div>

                    <!-- Main content area -->
                    <div class="col-lg-9 col-md-8">
                        <div class="cart-details-main-block" id="dynamic-cart">
                            <!-- White background card with rounded corners -->
                            <div class="bg-white p-8 rounded-10 mb-5 overflow-hidden position-relative">
                                <style>
                                .form-control,
                                .btn,
                                .filter-select {
                                    font-size: 16px;
                                }

                                /* Additional responsive styles for the search form */
                                @media (max-width: 576px) {
                                    .search-filter-row {
                                        margin: 0 -10px;
                                    }

                                    h2 {
                                        font-size: 1.5rem;
                                    }
                                }
                                </style>

                                <!-- Heading and search form styled like purchase-history.php -->
                                <h2 class="text-center mb-4">My Ticket Logs</h2>

                                <!-- Timezone indicator -->
                                <div class="text-center mb-3">
                                    <small class="text-muted">
                                        <i class="fas fa-clock"></i>
                                        Times shown in your timezone: <span
                                            id="customer-timezone-display"><?php echo getCustomerTimezone(); ?></span>
                                    </small>
                                </div>

                                <!-- Ticket Expiration Warning -->
                                <?php
                                include_once('../functions/ticket-expiration-functions.php');
                                $expiring_tickets = getTicketsExpiringSoon($username);
                                if (!empty($expiring_tickets)):
                                ?>
                                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>Ticket Expiration Warning!</strong>
                                    You have <?php echo count($expiring_tickets); ?> ticket purchase(s) expiring soon:
                                    <ul class="mb-0 mt-2">
                                        <?php foreach ($expiring_tickets as $ticket): ?>
                                        <li>
                                            <?php echo $ticket['remaining_tickets']; ?>
                                            <?php echo ucfirst($ticket['ticket_type']); ?> tickets
                                            (expires <?php echo showCustomerTimeSimple($ticket['expiration_date']); ?>)
                                        </li>
                                        <?php endforeach; ?>
                                    </ul>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                                <?php endif; ?>

                                <!-- Search form -->
                                <form method="GET" class="mb-4">
                                    <div class="search-filter-row d-flex justify-content-center flex-wrap">
                                        <div class="search-box w-100">
                                            <div class="input-group mx-auto">
                                                <input type="text" name="search" class="form-control search-input"
                                                    placeholder="Search by action, description or date..."
                                                    value="<?php echo htmlspecialchars($search); ?>">
                                                <div class="input-group-append">
                                                    <button type="submit" class="btn btn-primary search-button">
                                                        <i class="fas fa-search"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </form>




                                <?php if ($resultLogs && mysqli_num_rows($resultLogs) > 0) : ?>
                                <div class="table-responsive">
                                    <table class="table table-striped mx-auto" style="max-width: 90%;">
                                        <thead>
                                            <tr>
                                                <th class="text-center" scope="col">Date</th>
                                                <th class="text-center" scope="col">Status</th>
                                                <th class="text-center" scope="col">Ticket Type</th>
                                                <th class="text-center" scope="col">Amount</th>
                                                <th class="text-center" scope="col">Description</th>
                                                <th class="text-center" scope="col">Performed By</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php while ($log = mysqli_fetch_assoc($resultLogs)) : ?>
                                            <tr>
                                                <td class="text-center" scope="row">
                                                    <?php echo showCustomerTimeSimple($log['created_at']); ?></td>
                                                <td class="text-center" scope="row">
                                                    <?php
                                                                $actionClass = '';
                                                                $displayAction = $log['action'];

                                                                // Convert unknown actions to "Updated"
                                                                if (strtolower($log['action']) == 'unknown' || empty($log['action'])) {
                                                                    $displayAction = 'Updated';
                                                                }

                                                                switch (strtolower($displayAction)) {
                                                                    case 'success':
                                                                        $actionClass = 'badge-success';
                                                                        break;
                                                                    case 'pending':
                                                                        $actionClass = 'badge-pending';
                                                                        break;
                                                                    case 'fail':
                                                                        $actionClass = 'badge-fail';
                                                                        break;
                                                                    case 'cancel':
                                                                        $actionClass = 'badge-cancel';
                                                                        break;
                                                                    case 'updated':
                                                                        $actionClass = 'badge-updated';
                                                                        break;
                                                                    case 'expired':
                                                                        $actionClass = 'badge-expired';
                                                                        $displayAction = 'Expired';
                                                                        break;
                                                                    // Keep backward compatibility with old action types
                                                                    case 'purchase':
                                                                    case 'create':
                                                                        $actionClass = 'badge-success';
                                                                        break;
                                                                    case 'refund':
                                                                        $actionClass = 'badge-fail';
                                                                        break;
                                                                    case 'usage':
                                                                    case 'update':
                                                                        $actionClass = 'badge-pending';
                                                                        break;
                                                                    default:
                                                                        $actionClass = 'badge-secondary';
                                                                }
                                                                ?>
                                                    <span
                                                        class="badge <?php echo $actionClass; ?>"><?php echo ucfirst($displayAction); ?></span>
                                                </td>
                                                <td class="text-center" scope="row">
                                                    <?php
                                                                $ticketType = !empty($log['ticket_type']) ? $log['ticket_type'] : (!empty($log['ticket_type_name']) ? $log['ticket_type_name'] : 'unknown');
                                                                $typeClass = '';
                                                                $displayText = '';

                                                                // Convert unknown ticket types to "Updated"
                                                                if (strtolower($ticketType) == 'unknown' || empty($ticketType)) {
                                                                    $ticketType = 'updated';
                                                                    $displayText = 'Updated';
                                                                }

                                                                switch (strtolower($ticketType)) {
                                                                    case 'starter':
                                                                        $typeClass = 'badge-starter';
                                                                        $displayText = 'Starter';
                                                                        break;
                                                                    case 'premium':
                                                                        $typeClass = 'badge-premium';
                                                                        $displayText = 'Business';
                                                                        break;
                                                                    case 'ultimate':
                                                                        $typeClass = 'badge-ultimate';
                                                                        $displayText = 'Ultimate';
                                                                        break;
                                                                    case 'updated':
                                                                        $typeClass = 'badge-updated';
                                                                        $displayText = 'Updated';
                                                                        break;
                                                                    default:
                                                                        $typeClass = 'badge-secondary';
                                                                        $displayText = ucfirst($ticketType);
                                                                }
                                                                ?>
                                                    <span
                                                        class="badge <?php echo $typeClass; ?>"><?php echo $displayText; ?></span>
                                                </td>
                                                <td class="text-center" scope="row"><?php echo $log['amount']; ?></td>
                                                <td class="text-center" scope="row">
                                                    <?php
                                                    // Clean up description to show user-friendly messages only
                                                    $description = $log['description'];

                                                    // Remove API sync status information from ticket creation messages
                                                    // Convert "Created new starter ticket - 1 Ticket (Synced to API with createTicketByContact)"
                                                    // to "Created new starter ticket - 1 Ticket"
                                                    $description = preg_replace('/\s*\(API sync failed\)$/i', '', $description);
                                                    $description = preg_replace('/\s*\(Synced to API.*?\)$/i', '', $description);
                                                    $description = preg_replace('/\s*\(API.*?\)$/i', '', $description);

                                                    // Also remove any remaining sync-related text
                                                    $description = preg_replace('/\s*\(Synced to API with.*?\)$/i', '', $description);
                                                    $description = preg_replace('/\s*\(createTicketByContact\)$/i', '', $description);

                                                    echo htmlspecialchars($description);
                                                    ?>
                                                </td>
                                                <td class="text-center" scope="row">
                                                    <?php
                                                                if (!empty($log['performed_by_admin_id'])) {
                                                                    echo 'Admin #' . htmlspecialchars($log['performed_by_admin_id']);
                                                                } else {
                                                                    echo 'System';
                                                                }
                                                                ?>
                                                </td>
                                            </tr>
                                            <?php endwhile; ?>
                                        </tbody>
                                    </table>

                                    <!-- Pagination Controls -->
                                    <?php if ($totalPages > 1): ?>
                                    <div class="pagination-container mt-4">
                                        <nav aria-label="Page navigation">
                                            <ul class="pagination justify-content-center">
                                                <!-- Previous Button -->
                                                <li class="page-item <?php echo ($page <= 1) ? 'disabled' : ''; ?>">
                                                    <a class="page-link"
                                                        href="?page=<?php echo $page - 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>"
                                                        aria-label="Previous">
                                                        <span aria-hidden="true">&laquo; Previous</span>
                                                    </a>
                                                </li>

                                                <!-- Page Numbers -->
                                                <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                                <li class="page-item <?php echo ($page == $i) ? 'active' : ''; ?>">
                                                    <a class="page-link"
                                                        href="?page=<?php echo $i; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">
                                                        <?php echo $i; ?>
                                                    </a>
                                                </li>
                                                <?php endfor; ?>

                                                <!-- Next Button -->
                                                <li
                                                    class="page-item <?php echo ($page >= $totalPages) ? 'disabled' : ''; ?>">
                                                    <a class="page-link"
                                                        href="?page=<?php echo $page + 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>"
                                                        aria-label="Next">
                                                        <span aria-hidden="true">Next &raquo;</span>
                                                    </a>
                                                </li>
                                            </ul>
                                        </nav>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                <?php else : ?>
                                <div class="text-center mt-5">
                                    <?php if (!empty($search)) : ?>
                                    <p class="mb-3">No ticket logs found matching your search.</p>
                                    <p><a href="my-ticket-log.php" class="btn btn-outline-primary">Clear search</a></p>
                                    <?php else : ?>
                                    <p>No ticket logs found.</p>
                                    <?php endif; ?>
                                </div>
                                <?php endif; ?>
                            </div> <!-- End of white background card -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php else : ?>
        <div class="d-flex justify-content-center align-items-center full-height">
            <div class="text-center">
                <?php echo "You are not logged in."; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Footer section -->
        <?php include('../header-footer/footer.php'); ?>
    </div>

    <!-- Vendor Scripts -->
    <script src="../js/vendor.min.js"></script>
    <!-- Plugin's Scripts -->
    <script src="../plugins/fancybox/jquery.fancybox.min.js"></script>
    <script src="../plugins/nice-select/jquery.nice-select.min.js"></script>
    <script src="../plugins/aos/aos.min.js"></script>
    <script src="../plugins/slick/slick.min.js"></script>
    <script src="../plugins/date-picker/js/gijgo.min.js"></script>
    <script src="../plugins/counter-up/jquery.waypoints.min.js"></script>
    <script src="../plugins/counter-up/jquery.counterup.min.js"></script>
    <script src="../plugins/theme-mode-switcher/gr-theme-mode-switcher.js"></script>
    <!-- Customer Timezone Detection -->
    <script src="../js/customer-timezone.js"></script>

    <!-- Update timezone display when detected -->
    <script>
    document.addEventListener('customerTimezoneDetected', function(event) {
        const timezoneDisplay = document.getElementById('customer-timezone-display');
        if (timezoneDisplay) {
            timezoneDisplay.textContent = event.detail.timezone;
        }
        console.log('Customer timezone updated in display:', event.detail.timezone);
    });

    // Also update on page load if timezone is already available
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(function() {
            if (window.CustomerTimezone) {
                const timezone = window.CustomerTimezone.getCustomerTimezone();
                const timezoneDisplay = document.getElementById('customer-timezone-display');
                if (timezoneDisplay && timezone) {
                    timezoneDisplay.textContent = timezone;
                }
            }
        }, 1000);
    });

    // Auto-update expiration warnings every 5 seconds
    function updateExpirationWarnings() {
        fetch('get-ticket-counts.php', {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update expiration warning
                const existingWarning = document.querySelector('.alert-warning');
                if (data.expiring_tickets_count > 0) {
                    if (!existingWarning) {
                        // Create warning if it doesn't exist
                        const warningHtml = `
                            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>Ticket Expiration Warning!</strong>
                                You have ${data.expiring_tickets_count} ticket purchase(s) expiring soon.
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        `;
                        const searchForm = document.querySelector('form[method="GET"]');
                        searchForm.insertAdjacentHTML('beforebegin', warningHtml);
                    }
                } else if (existingWarning) {
                    // Remove warning if no expiring tickets
                    existingWarning.remove();
                }

                console.log('Ticket log: Expiration warnings updated:', data);
            }
        })
        .catch(error => console.error('Error updating expiration warnings:', error));
    }

    // Start auto-update when page loads
    document.addEventListener('DOMContentLoaded', function() {
        // Update immediately
        updateExpirationWarnings();
        // Then update every 5 seconds
        setInterval(updateExpirationWarnings, 5000);
    });
    </script>

    <!-- Activation Script -->
    <script src="../js/custom.js"></script>
</body>

</html>